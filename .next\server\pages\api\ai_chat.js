"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/ai_chat";
exports.ids = ["pages/api/ai_chat"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "openai":
/*!*************************!*\
  !*** external "openai" ***!
  \*************************/
/***/ ((module) => {

module.exports = import("openai");;

/***/ }),

/***/ "uuid":
/*!***********************!*\
  !*** external "uuid" ***!
  \***********************/
/***/ ((module) => {

module.exports = import("uuid");;

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai_chat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai_chat.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai_chat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai_chat.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_ai_chat_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\ai_chat.ts */ \"(api)/./src/pages/api/ai_chat.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_api_ai_chat_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_src_pages_api_ai_chat_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_ai_chat_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_ai_chat_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/ai_chat\",\n        pathname: \"/api/ai_chat\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_ai_chat_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmFpX2NoYXQmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZzcmMlNUNwYWdlcyU1Q2FwaSU1Q2FpX2NoYXQudHMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ0U7QUFDMUQ7QUFDMEQ7QUFDMUQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLHNEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxzREFBUTtBQUNwQztBQUNPLHdCQUF3Qix5R0FBbUI7QUFDbEQ7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQscUMiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3NyY1xcXFxwYWdlc1xcXFxhcGlcXFxcYWlfY2hhdC50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCAnZGVmYXVsdCcpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgJ2NvbmZpZycpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNBUElSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVNfQVBJLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYWlfY2hhdFwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2FpX2NoYXRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai_chat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai_chat.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/data/sp500_enriched_final.ts":
/*!******************************************!*\
  !*** ./src/data/sp500_enriched_final.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QUICK_ENRICHED_FINAL: () => (/* binding */ QUICK_ENRICHED_FINAL)\n/* harmony export */ });\nconst QUICK_ENRICHED_FINAL = {\n    \"A\": {\n        name: \"Agilent Technologies\",\n        description: \"Provides life‑science and chemical analysis instruments, software and lab services.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"AAPL\": {\n        name: \"Apple Inc.\",\n        description: \"Designs iPhone, Mac and a growing ecosystem of consumer devices and digital services.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"ABBV\": {\n        name: \"AbbVie\",\n        description: \"Develops and markets specialty pharmaceuticals such as the immunology drug Humira.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"ABNB\": {\n        name: \"Airbnb\",\n        description: \"Runs a marketplace that matches travelers with short‑term home and experience rentals.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"ABT\": {\n        name: \"Abbott Laboratories\",\n        description: \"Supplies diagnostic equipment, medical devices and nutritional products worldwide.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"ACGL\": {\n        name: \"Arch Capital Group\",\n        description: \"Provides specialty insurance, reinsurance and mortgage insurance globally.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"ACN\": {\n        name: \"Accenture\",\n        description: \"Provides global consulting, technology outsourcing and digital transformation services.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"ADBE\": {\n        name: \"Adobe Inc.\",\n        description: \"Offers subscription software for creative design, digital documents and marketing analytics.\",\n        industry: \"Application Software\"\n    },\n    \"ADI\": {\n        name: \"Analog Devices\",\n        description: \"Supplies analog and mixed‑signal semiconductors used in sensing and power management.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"ADM\": {\n        name: \"Archer Daniels Midland\",\n        description: \"Processes crops into food ingredients, animal feed and biofuels.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"ADP\": {\n        name: \"Automatic Data Processing\",\n        description: \"Provides cloud‑based payroll, HR and workforce management solutions for employers.\",\n        industry: \"Application Software\"\n    },\n    \"ADSK\": {\n        name: \"Autodesk\",\n        description: \"Offers 3‑D design and engineering software such as AutoCAD and Revit via cloud subscriptions.\",\n        industry: \"Application Software\"\n    },\n    \"AEE\": {\n        name: \"Ameren\",\n        description: \"Generates and distributes electricity and natural gas in Missouri and Illinois.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"AEP\": {\n        name: \"American Electric Power\",\n        description: \"Operates one of the largest transmission grids and generation fleets in the U.S.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"AES\": {\n        name: \"AES Corporation\",\n        description: \"Generates and distributes electricity through a portfolio of global power plants.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"AFL\": {\n        name: \"Aflac\",\n        description: \"Sells supplemental health and accident insurance, primarily in the United States and Japan.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"AIG\": {\n        name: \"American International Group\",\n        description: \"Provides property‑casualty, life and retirement insurance solutions worldwide.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"AIZ\": {\n        name: \"Assurant\",\n        description: \"Provides specialty insurance covering mobile devices, extended warranties and lender services.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"AJG\": {\n        name: \"Arthur J. Gallagher & Co.\",\n        description: \"Offers insurance brokerage and risk management services to businesses worldwide.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"AKAM\": {\n        name: \"Akamai Technologies\",\n        description: \"Delivers content‑delivery and cloud security services that speed and protect web traffic.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"ALB\": {\n        name: \"Albemarle Corporation\",\n        description: \"Produces lithium, bromine and other specialty chemicals for batteries and industrial uses.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"ALGN\": {\n        name: \"Align Technology\",\n        description: \"Manufactures the Invisalign clear dental aligner system and 3‑D scanners.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"ALL\": {\n        name: \"Allstate\",\n        description: \"Offers auto, homeowners and other personal property‑casualty insurance.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"ALLE\": {\n        name: \"Allegion\",\n        description: \"Makes commercial and residential locks, door hardware and access‑control systems.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"AMAT\": {\n        name: \"Applied Materials\",\n        description: \"Supplies semiconductor fabrication equipment and process materials to chipmakers.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"AMCR\": {\n        name: \"Amcor\",\n        description: \"Produces flexible and rigid packaging for food, beverage and healthcare products.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"AMD\": {\n        name: \"Advanced Micro Devices\",\n        description: \"Designs CPUs and GPUs for PCs, gaming consoles and data‑center servers, outsourcing fabrication.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"AME\": {\n        name: \"Ametek\",\n        description: \"Produces electronic instruments and electromechanical components for industrial markets.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"AMGN\": {\n        name: \"Amgen\",\n        description: \"Researches and manufactures biologic therapies for oncology, inflammation and rare diseases.\",\n        industry: \"Biotechnology\"\n    },\n    \"AMP\": {\n        name: \"Ameriprise Financial\",\n        description: \"Offers wealth management, asset management and annuity products.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"AMT\": {\n        name: \"American Tower\",\n        description: \"Leases wireless towers and data centers to mobile network operators across the globe.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"AMZN\": {\n        name: \"Amazon\",\n        description: \"Runs the world’s largest e‑commerce marketplace and the AWS cloud infrastructure business.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"ANET\": {\n        name: \"Arista Networks\",\n        description: \"Develops high‑performance Ethernet switches and cloud networking software.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"ANSS\": {\n        name: \"Ansys\",\n        description: \"Provides engineering simulation software used for product design and testing.\",\n        industry: \"Application Software\"\n    },\n    \"AON\": {\n        name: \"Aon plc\",\n        description: \"Delivers insurance brokerage, reinsurance and risk advisory services.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"AOS\": {\n        name: \"A. O. Smith\",\n        description: \"Designs and manufactures residential and commercial water heaters and boilers.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"APA\": {\n        name: \"APA Corporation\",\n        description: \"Explores for and produces oil and natural gas resources in the U.S. and overseas.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"APD\": {\n        name: \"Air Products\",\n        description: \"Supplies industrial and specialty gases such as hydrogen, nitrogen and oxygen.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"APH\": {\n        name: \"Amphenol\",\n        description: \"Designs and manufactures electronic connectors, cables and antenna solutions.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"APO\": {\n        name: \"Apollo Global Management\",\n        description: \"Manages private equity, credit and real‑estate funds for institutional investors.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"APTV\": {\n        name: \"Aptiv\",\n        description: \"Produces advanced electrical architectures and software for automotive safety and connectivity.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"ARE\": {\n        name: \"Alexandria Real Estate Equities\",\n        description: \"Owns and leases life‑science laboratory campuses to biotech and pharma tenants.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"ATO\": {\n        name: \"Atmos Energy\",\n        description: \"Delivers natural gas utility service to customers in the southern United States.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"AVB\": {\n        name: \"AvalonBay Communities\",\n        description: \"Develops and manages multifamily apartment communities in high‑growth U.S. markets.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"AVGO\": {\n        name: \"Broadcom\",\n        description: \"Designs high‑performance semiconductor chips for networking, wireless and storage.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"AVY\": {\n        name: \"Avery Dennison\",\n        description: \"Produces pressure‑sensitive labels, RFID tags and packaging materials.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"AWK\": {\n        name: \"American Water Works\",\n        description: \"Owns regulated water and wastewater utilities serving customers in multiple states.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"AXON\": {\n        name: \"Axon Enterprise\",\n        description: \"Manufactures TASER devices, body cameras and digital evidence management software for public safety.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"AXP\": {\n        name: \"American Express\",\n        description: \"Issues premium charge and credit cards and operates a global payments network.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"AZO\": {\n        name: \"AutoZone\",\n        description: \"Operates a chain of retail stores supplying replacement auto parts and accessories.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"BA\": {\n        name: \"Boeing\",\n        description: \"Manufactures commercial jetliners, defense aircraft and space systems.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"BAC\": {\n        name: \"Bank of America\",\n        description: \"Offers retail and investment banking, credit cards and wealth management services.\",\n        industry: \"Banks\"\n    },\n    \"BALL\": {\n        name: \"Ball Corporation\",\n        description: \"Makes recyclable aluminum beverage cans and aerospace components.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"BAX\": {\n        name: \"Baxter International\",\n        description: \"Supplies hospital products and medical devices for critical care and dialysis.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"BBY\": {\n        name: \"Best Buy\",\n        description: \"Runs a nationwide chain of big‑box stores selling consumer electronics and appliances.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"BDX\": {\n        name: \"Becton Dickinson\",\n        description: \"Manufactures needles, syringes and diagnostic instruments used in hospitals and labs.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"BEN\": {\n        name: \"Franklin Resources\",\n        description: \"Manages mutual funds and institutional assets under the Franklin Templeton brand.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"BG\": {\n        name: \"Bunge Global\",\n        description: \"Processes oilseeds and grains into food ingredients, animal feed and biofuels.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"BIIB\": {\n        name: \"Biogen\",\n        description: \"Develops therapies for neurological diseases such as multiple sclerosis and ALS.\",\n        industry: \"Biotechnology\"\n    },\n    \"BK\": {\n        name: \"BNY Mellon\",\n        description: \"Provides custodial banking, clearing and asset‑servicing for institutional investors.\",\n        industry: \"Banks\"\n    },\n    \"BKNG\": {\n        name: \"Booking Holdings\",\n        description: \"Operates Booking.com, Priceline and other platforms that reserve lodging and travel services.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"BKR\": {\n        name: \"Baker Hughes\",\n        description: \"Provides oilfield services, equipment and technology to the energy industry.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"BLDR\": {\n        name: \"Builders FirstSource\",\n        description: \"Distributes lumber and prefabricated components to residential homebuilders.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"BLK\": {\n        name: \"BlackRock\",\n        description: \"Manages global index and active funds, including the iShares ETF family.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"BMY\": {\n        name: \"Bristol Myers Squibb\",\n        description: \"Researches and markets prescription drugs for oncology, immunology and cardiovascular care.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"BR\": {\n        name: \"Broadridge Financial Solutions\",\n        description: \"Provides investor communications, proxy processing and fintech back‑office platforms.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"BRO\": {\n        name: \"Brown & Brown\",\n        description: \"Brokers property‑and‑casualty insurance and employee benefits for businesses.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"BSX\": {\n        name: \"Boston Scientific\",\n        description: \"Makes minimally invasive medical devices like cardiac stents and catheters.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"BX\": {\n        name: \"Blackstone Inc.\",\n        description: \"Operates private‑equity, credit, real‑estate and hedge‑fund investment vehicles.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"BXP\": {\n        name: \"BXP, Inc.\",\n        description: \"Owns and manages Class‑A office buildings in major U.S. gateway cities.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"C\": {\n        name: \"Citigroup\",\n        description: \"Provides global consumer banking, credit cards and institutional financial services.\",\n        industry: \"Banks\"\n    },\n    \"CAG\": {\n        name: \"Conagra Brands\",\n        description: \"Produces frozen foods, snacks and condiments under brands like Healthy Choice and Slim Jim.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"CAH\": {\n        name: \"Cardinal Health\",\n        description: \"Distributes pharmaceuticals and medical‑surgical supplies to hospitals and pharmacies.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"CARR\": {\n        name: \"Carrier Global\",\n        description: \"Manufactures HVAC, refrigeration and fire‑safety equipment for buildings.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"CAT\": {\n        name: \"Caterpillar\",\n        description: \"Produces heavy construction, mining and energy equipment under the Caterpillar brand.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"CB\": {\n        name: \"Chubb Limited\",\n        description: \"Provides global property, casualty and reinsurance coverage to commercial and personal clients.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"CBOE\": {\n        name: \"Cboe Global Markets\",\n        description: \"Operates options, equities and futures exchanges along with market‑data services.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"CBRE\": {\n        name: \"CBRE Group\",\n        description: \"Provides commercial real‑estate brokerage, investment management and facilities services.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"CCI\": {\n        name: \"Crown Castle\",\n        description: \"Owns and leases wireless towers, small cells and fiber for mobile carriers across the U.S.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"CCL\": {\n        name: \"Carnival\",\n        description: \"Runs Carnival, Princess and other cruise lines offering global vacation voyages.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"CDNS\": {\n        name: \"Cadence Design Systems\",\n        description: \"Offers electronic design automation software used to design complex semiconductors.\",\n        industry: \"Application Software\"\n    },\n    \"CDW\": {\n        name: \"CDW Corporation\",\n        description: \"Resells IT hardware, software and cloud solutions to businesses and governments.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"CEG\": {\n        name: \"Constellation Energy\",\n        description: \"Generates electricity primarily from nuclear and renewable assets and sells it wholesale.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"CF\": {\n        name: \"CF Industries\",\n        description: \"Produces nitrogen‑based fertilizers such as ammonia, urea and UAN for agriculture.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"CFG\": {\n        name: \"Citizens Financial Group\",\n        description: \"Operates regional retail and commercial banking centered in New England and the Midwest.\",\n        industry: \"Banks\"\n    },\n    \"CHD\": {\n        name: \"Church & Dwight\",\n        description: \"Markets household and personal‑care brands including Arm & Hammer and Trojan.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"CHRW\": {\n        name: \"C.H. Robinson\",\n        description: \"Arranges freight shipments and supply‑chain services as a third‑party logistics provider.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"CHTR\": {\n        name: \"Charter Communications\",\n        description: \"Supplies cable television, broadband internet and voice services under the Spectrum brand.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"CI\": {\n        name: \"Cigna\",\n        description: \"Offers medical, dental and pharmacy benefit plans and runs a large PBM unit.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"CINF\": {\n        name: \"Cincinnati Financial\",\n        description: \"Underwrites commercial and personal property‑and‑casualty insurance through independent agents.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"CL\": {\n        name: \"Colgate-Palmolive\",\n        description: \"Sells Colgate toothpaste, Palmolive soaps and other oral and personal‑care products.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"CLX\": {\n        name: \"Clorox\",\n        description: \"Produces bleach, disinfecting wipes and household cleaning products under Clorox and other brands.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"CMCSA\": {\n        name: \"Comcast\",\n        description: \"Provides broadband, cable TV and owns NBCUniversal’s media and theme‑park assets.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"CME\": {\n        name: \"CME Group\",\n        description: \"Runs futures and options exchanges trading commodities, rates and equities worldwide.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"CMG\": {\n        name: \"Chipotle Mexican Grill\",\n        description: \"Operates fast‑casual restaurants serving made‑to‑order burritos and bowls.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"CMI\": {\n        name: \"Cummins\",\n        description: \"Manufactures diesel and alternative‑fuel engines, powertrains and generators for trucks and equipment.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"CMS\": {\n        name: \"CMS Energy\",\n        description: \"Generates and distributes electricity and gas to customers across Michigan.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"CNC\": {\n        name: \"Centene\",\n        description: \"Offers managed‑care health plans focused on Medicaid and government programs.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"CNP\": {\n        name: \"CenterPoint Energy\",\n        description: \"Delivers regulated electric and natural‑gas service in Texas and neighboring states.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"COF\": {\n        name: \"Capital One Financial\",\n        description: \"Issues credit cards and provides retail and commercial banking services in the U.S.\",\n        industry: \"Banks\"\n    },\n    \"COIN\": {\n        name: \"Coinbase\",\n        description: \"Operates a regulated cryptocurrency exchange, custody and blockchain services platform.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"COO\": {\n        name: \"Cooper Companies (The)\",\n        description: \"Produces contact lenses and surgical devices for women's health under CooperVision and CooperSurgical.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"COP\": {\n        name: \"ConocoPhillips\",\n        description: \"Explores and produces crude oil and natural gas across global basins.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"COR\": {\n        name: \"Cencora\",\n        description: \"Provides drug‑distribution and services to pharmaceutical manufacturers and providers.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"COST\": {\n        name: \"Costco\",\n        description: \"Operates membership‑only warehouse clubs selling bulk groceries, appliances and general merchandise.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"CPAY\": {\n        name: \"Corpay\",\n        description: \"Provides cross‑border B2B payment and expense management solutions to enterprises.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"CPB\": {\n        name: \"Campbell Soup Company\",\n        description: \"Produces branded soups, snacks and meals under Campbell's, Pepperidge Farm and V8.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"CPRT\": {\n        name: \"Copart\",\n        description: \"Runs online auctions for salvage and used vehicles, handling storage, logistics and title transfer.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"CPT\": {\n        name: \"Camden Property Trust\",\n        description: \"Develops, owns and leases multifamily apartment communities in high‑growth markets.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"CRL\": {\n        name: \"Charles River Laboratories\",\n        description: \"Runs preclinical CRO labs that test the safety and efficacy of new drug candidates.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"CRM\": {\n        name: \"Salesforce\",\n        description: \"Delivers cloud customer relationship management software under the Salesforce platform.\",\n        industry: \"Application Software\"\n    },\n    \"CRWD\": {\n        name: \"CrowdStrike\",\n        description: \"Offers Falcon endpoint security platform for real‑time threat detection and response.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"CSCO\": {\n        name: \"Cisco Systems\",\n        description: \"Builds networking routers, switches and security hardware plus collaboration software.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"CSGP\": {\n        name: \"CoStar Group\",\n        description: \"Supplies commercial real‑estate listings, analytics and marketplaces like LoopNet and Apartments.com.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"CSX\": {\n        name: \"CSX Corporation\",\n        description: \"Operates a major U.S. freight railroad network serving the eastern United States.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"CTAS\": {\n        name: \"Cintas\",\n        description: \"Rents uniforms, mats and facility supplies to businesses across North America.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"CTRA\": {\n        name: \"Coterra\",\n        description: \"Explores for and produces oil and natural gas in the Permian, Marcellus and Anadarko basins.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"CTSH\": {\n        name: \"Cognizant Technology Solutions\",\n        description: \"Delivers IT consulting, digital engineering and business‑process outsourcing services.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"CTVA\": {\n        name: \"Corteva\",\n        description: \"Develops crop‑protection chemicals and high‑yield seeds for global agriculture markets.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"CVS\": {\n        name: \"CVS Health\",\n        description: \"Runs CVS pharmacies, MinuteClinics and a large pharmacy‑benefit and health‑insurance business.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"CVX\": {\n        name: \"Chevron\",\n        description: \"Explores, produces and refines oil and natural gas on a global scale.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"CZR\": {\n        name: \"Caesars Entertainment\",\n        description: \"Operates casino resorts and digital sports‑betting platforms across North America.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"D\": {\n        name: \"Dominion Energy\",\n        description: \"Generates and distributes regulated electricity and natural gas in the Mid‑Atlantic region.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"DAL\": {\n        name: \"Delta Air Lines\",\n        description: \"Provides global passenger and cargo air transportation with a main hub in Atlanta.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"DASH\": {\n        name: \"DoorDash\",\n        description: \"Operates an on‑demand logistics platform delivering restaurant meals and retail goods.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"DAY\": {\n        name: \"Dayforce\",\n        description: \"Sells Dayforce cloud software for human‑capital management and payroll processing.\",\n        industry: \"Application Software\"\n    },\n    \"DD\": {\n        name: \"DuPont\",\n        description: \"Produces specialty materials, resins and electronics materials for industrial applications.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"DE\": {\n        name: \"Deere & Company\",\n        description: \"Builds John Deere tractors, combines and precision agriculture machinery.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"DECK\": {\n        name: \"Deckers Brands\",\n        description: \"Designs and markets footwear brands including UGG, HOKA ONE ONE and Teva.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"DELL\": {\n        name: \"Dell Technologies\",\n        description: \"Sells personal computers, servers, storage and IT services to consumers and enterprises.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"DG\": {\n        name: \"Dollar General\",\n        description: \"Runs Dollar General discount stores offering low‑priced everyday household items.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"DGX\": {\n        name: \"Quest Diagnostics\",\n        description: \"Operates a network of medical laboratories providing diagnostic testing services.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"DHI\": {\n        name: \"D. R. Horton\",\n        description: \"Constructs and sells single‑family homes and residential communities across the U.S.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"DHR\": {\n        name: \"Danaher Corporation\",\n        description: \"Supplies life‑science instruments, diagnostics and water‑quality equipment through a portfolio of subsidiaries.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"DIS\": {\n        name: \"Walt Disney Company (The)\",\n        description: \"Produces films, streaming content, theme parks and consumer products.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"DLR\": {\n        name: \"Digital Realty\",\n        description: \"Owns and leases carrier‑neutral data centers supporting cloud and enterprise workloads.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"DLTR\": {\n        name: \"Dollar Tree\",\n        description: \"Operates Dollar Tree and Family Dollar variety stores offering discounted merchandise.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"DOC\": {\n        name: \"Healthpeak Properties\",\n        description: \"Owns medical office buildings and life‑science campuses as a healthcare REIT.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"DOV\": {\n        name: \"Dover Corporation\",\n        description: \"Manufactures industrial components such as pumps, compressors and marking systems.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"DOW\": {\n        name: \"Dow Inc.\",\n        description: \"Produces basic plastics, chemicals and intermediates used in packaging and construction.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"DPZ\": {\n        name: \"Domino's\",\n        description: \"Franchises Domino’s pizza restaurants offering delivery and carry‑out service worldwide.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"DRI\": {\n        name: \"Darden Restaurants\",\n        description: \"Operates full‑service restaurant chains including Olive Garden and LongHorn Steakhouse.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"DTE\": {\n        name: \"DTE Energy\",\n        description: \"Generates electricity and distributes gas and power to customers in Michigan.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"DUK\": {\n        name: \"Duke Energy\",\n        description: \"Provides electric and gas utility service across the Carolinas, Florida and Midwest.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"DVA\": {\n        name: \"DaVita\",\n        description: \"Operates outpatient dialysis centers treating patients with chronic kidney failure.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"DVN\": {\n        name: \"Devon Energy\",\n        description: \"Explores and produces oil and gas with a focus on the Delaware and Anadarko basins.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"DXCM\": {\n        name: \"Dexcom\",\n        description: \"Develops continuous glucose monitoring systems for diabetes management.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"EA\": {\n        name: \"Electronic Arts\",\n        description: \"Creates and publishes video game franchises such as FIFA, Madden NFL and The Sims.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"EBAY\": {\n        name: \"eBay Inc.\",\n        description: \"Runs an online marketplace connecting sellers and buyers of new and used goods.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"ECL\": {\n        name: \"Ecolab\",\n        description: \"Provides water, hygiene and infection‑prevention chemicals and services for hospitality and industry.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"ED\": {\n        name: \"Consolidated Edison\",\n        description: \"Supplies electric and steam service to New York City and gas to surrounding areas.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"EFX\": {\n        name: \"Equifax\",\n        description: \"Maintains consumer credit databases and offers identity‑verification analytics.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"EG\": {\n        name: \"Everest Group\",\n        description: \"Offers specialty reinsurance and insurance products to global clients.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"EIX\": {\n        name: \"Edison International\",\n        description: \"Supplies electricity to Southern California through its regulated utility SCE.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"EL\": {\n        name: \"Estée Lauder Companies (The)\",\n        description: \"Markets prestige skincare, makeup and fragrance brands including Estée Lauder and MAC.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"ELV\": {\n        name: \"Elevance Health\",\n        description: \"Provides Blue Cross Blue Shield‑branded health insurance plans across the U.S.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"EMN\": {\n        name: \"Eastman Chemical Company\",\n        description: \"Makes specialty plastics, additives and fibers derived from acetyl and polyester chemistry.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"EMR\": {\n        name: \"Emerson Electric\",\n        description: \"Provides industrial automation systems, valves and measurement equipment.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"ENPH\": {\n        name: \"Enphase Energy\",\n        description: \"Designs microinverters and battery storage solutions for residential solar energy systems.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"EOG\": {\n        name: \"EOG Resources\",\n        description: \"Produces crude oil and natural gas with a focus on shale plays in the U.S.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"EPAM\": {\n        name: \"EPAM Systems\",\n        description: \"Offers outsourced software engineering and digital product design services.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"EQIX\": {\n        name: \"Equinix\",\n        description: \"Operates global colocation data centers interconnecting cloud and network providers.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"EQR\": {\n        name: \"Equity Residential\",\n        description: \"Owns and manages high‑end apartment communities in urban U.S. markets.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"EQT\": {\n        name: \"EQT Corporation\",\n        description: \"Produces natural gas primarily from the Marcellus and Utica shales.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"ERIE\": {\n        name: \"Erie Indemnity\",\n        description: \"Provides auto, home and business insurance through independent agents.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"ES\": {\n        name: \"Eversource Energy\",\n        description: \"Delivers regulated electric and natural‑gas service in New England.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"ESS\": {\n        name: \"Essex Property Trust\",\n        description: \"Owns Class‑A multifamily properties on the U.S. West Coast.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"ETN\": {\n        name: \"Eaton Corporation\",\n        description: \"Supplies electrical power‑management equipment, hydraulics and aerospace components.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"ETR\": {\n        name: \"Entergy\",\n        description: \"Generates nuclear and fossil power and distributes electricity in the Gulf South.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"EXE\": {\n        name: \"Expand Energy\",\n        description: \"Information unavailable.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"EXPD\": {\n        name: \"Expeditors International\",\n        description: \"Provides freight forwarding and customs brokerage services across air, ocean and ground routes.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"EXPE\": {\n        name: \"Expedia Group\",\n        description: \"Operates global online travel agencies including Expedia, Hotels.com and Vrbo.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"EXR\": {\n        name: \"Extra Space Storage\",\n        description: \"Owns and manages self‑storage facilities across the United States.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"F\": {\n        name: \"Ford Motor Company\",\n        description: \"Designs, manufactures and sells Ford and Lincoln vehicles and related services worldwide.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"FAST\": {\n        name: \"Fastenal\",\n        description: \"Distributes industrial and construction fasteners, tools and safety supplies.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"FCX\": {\n        name: \"Freeport-McMoRan\",\n        description: \"Mines copper, gold and molybdenum with major operations in the Americas and Indonesia.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"FDS\": {\n        name: \"FactSet\",\n        description: \"Delivers financial data, analytics and workflow tools to investment professionals.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"FDX\": {\n        name: \"FedEx\",\n        description: \"Provides global express parcel delivery, ground shipping and logistics solutions.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"FE\": {\n        name: \"FirstEnergy\",\n        description: \"Generates and distributes regulated electricity to customers in the U.S. Midwest and Mid‑Atlantic.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"FFIV\": {\n        name: \"F5, Inc.\",\n        description: \"Supplies application‑delivery and security platforms that optimize and protect network traffic.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"FI\": {\n        name: \"Fiserv\",\n        description: \"Provides payment processing, core banking and merchant acquiring technology.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"FICO\": {\n        name: \"Fair Isaac\",\n        description: \"Creates FICO credit scores and decision‑analytics software for lenders.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"FIS\": {\n        name: \"Fidelity National Information Services\",\n        description: \"Supplies core banking, card processing and treasury software to financial institutions.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"FITB\": {\n        name: \"Fifth Third Bancorp\",\n        description: \"Operates a regional banking network offering loans, deposits and payment services.\",\n        industry: \"Banks\"\n    },\n    \"FOX\": {\n        name: \"Fox Corporation (Class B)\",\n        description: \"Operates Fox News, Fox Sports and broadcast television stations in the United States.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"FOXA\": {\n        name: \"Fox Corporation (Class A)\",\n        description: \"Operates Fox News, Fox Sports and broadcast television stations in the United States.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"FRT\": {\n        name: \"Federal Realty Investment Trust\",\n        description: \"Owns and redevelops grocery‑anchored shopping centers in major U.S. metros.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"FSLR\": {\n        name: \"First Solar\",\n        description: \"Manufactures thin‑film photovoltaic modules and develops utility‑scale solar projects.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"FTNT\": {\n        name: \"Fortinet\",\n        description: \"Develops network firewalls and security operating systems for enterprise cyber defense.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"FTV\": {\n        name: \"Fortive\",\n        description: \"Produces industrial measurement, sensing and automation equipment through diversified brands.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"GD\": {\n        name: \"General Dynamics\",\n        description: \"Builds military vehicles, submarines, IT systems and Gulfstream business jets.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"GDDY\": {\n        name: \"GoDaddy\",\n        description: \"Provides domain registration, web hosting and small‑business cloud services.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"GE\": {\n        name: \"GE Aerospace\",\n        description: \"Manufactures jet and turboprop engines for commercial and military aircraft.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"GEHC\": {\n        name: \"GE HealthCare\",\n        description: \"Supplies medical imaging, ultrasound and patient monitoring equipment worldwide.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"GEN\": {\n        name: \"Gen Digital\",\n        description: \"Offers Norton and LifeLock cybersecurity software for consumers and small businesses.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"GEV\": {\n        name: \"GE Vernova\",\n        description: \"Produces wind turbines, grid solutions and power‑generation services focused on clean energy.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"GILD\": {\n        name: \"Gilead Sciences\",\n        description: \"Develops antiviral and oncology therapeutics including HIV and hepatitis C treatments.\",\n        industry: \"Biotechnology\"\n    },\n    \"GIS\": {\n        name: \"General Mills\",\n        description: \"Produces branded cereals, snacks and refrigerated meals such as Cheerios and Yoplait.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"GL\": {\n        name: \"Globe Life\",\n        description: \"Offers life and supplemental health insurance targeting middle‑income households.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"GM\": {\n        name: \"General Motors\",\n        description: \"Designs and manufactures Chevrolet, GMC, Cadillac and Buick vehicles worldwide.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"GNRC\": {\n        name: \"Generac\",\n        description: \"Manufactures standby generators and energy storage systems for residential and commercial use.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"GOOG\": {\n        name: \"Alphabet Inc. (Class C)\",\n        description: \"Operates Google search, YouTube, Android and a growing cloud computing platform.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"GOOGL\": {\n        name: \"Alphabet Inc. (Class A)\",\n        description: \"Operates Google search, YouTube, Android and a growing cloud computing platform.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"GPC\": {\n        name: \"Genuine Parts Company\",\n        description: \"Distributes automotive replacement parts through the NAPA brand and industrial components.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"GPN\": {\n        name: \"Global Payments\",\n        description: \"Provides merchant acquiring, card issuing and point‑of‑sale payment solutions.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"GRMN\": {\n        name: \"Garmin\",\n        description: \"Designs GPS navigation devices, fitness wearables and avionics systems.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"GS\": {\n        name: \"Goldman Sachs\",\n        description: \"Delivers investment banking, trading, asset management and consumer banking services.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"GWW\": {\n        name: \"W. W. Grainger\",\n        description: \"Distributes maintenance, repair and safety supplies to industrial customers.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"HAL\": {\n        name: \"Halliburton\",\n        description: \"Provides drilling, completions and production services to the oil and gas industry.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"HAS\": {\n        name: \"Hasbro\",\n        description: \"Designs and markets toys, games and entertainment content such as Monopoly and Transformers.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"HBAN\": {\n        name: \"Huntington Bancshares\",\n        description: \"Operates community banking branches offering consumer and commercial financial services.\",\n        industry: \"Banks\"\n    },\n    \"HCA\": {\n        name: \"HCA Healthcare\",\n        description: \"Operates hospitals and outpatient surgery centers across the United States.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"HD\": {\n        name: \"Home Depot (The)\",\n        description: \"Operates big‑box stores selling home‑improvement products and building materials.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"HES\": {\n        name: \"Hess Corporation\",\n        description: \"Explores and produces crude oil and natural gas in North Dakota, Guyana and other regions.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"HIG\": {\n        name: \"Hartford (The)\",\n        description: \"Offers property‑casualty, group benefits and mutual fund products.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"HII\": {\n        name: \"Huntington Ingalls Industries\",\n        description: \"Designs and builds nuclear‑powered aircraft carriers and submarines for the U.S. Navy.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"HLT\": {\n        name: \"Hilton Worldwide\",\n        description: \"Franchises and manages hotel brands including Hilton, Waldorf Astoria and Hampton.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"HOLX\": {\n        name: \"Hologic\",\n        description: \"Develops diagnostic imaging and testing products focused on women's health.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"HON\": {\n        name: \"Honeywell\",\n        description: \"Produces aerospace systems, industrial automation and building technologies as a diversified manufacturer.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"HPE\": {\n        name: \"Hewlett Packard Enterprise\",\n        description: \"Provides servers, storage and edge‑to‑cloud IT solutions for enterprises.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"HPQ\": {\n        name: \"HP Inc.\",\n        description: \"Produces personal computers, printers and related supplies for consumers and businesses.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"HRL\": {\n        name: \"Hormel Foods\",\n        description: \"Processes and markets branded meat and food products such as Spam and Skippy.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"HSIC\": {\n        name: \"Henry Schein\",\n        description: \"Distributes dental and medical supplies along with practice‑management software.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"HST\": {\n        name: \"Host Hotels & Resorts\",\n        description: \"Owns upscale hotel properties operated under leading hospitality brands.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"HSY\": {\n        name: \"Hershey Company (The)\",\n        description: \"Produces chocolate and confectionery brands such as Hershey's, Reese's and KitKat.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"HUBB\": {\n        name: \"Hubbell Incorporated\",\n        description: \"Manufactures electrical wiring, lighting and utility infrastructure products.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"HUM\": {\n        name: \"Humana\",\n        description: \"Provides Medicare Advantage and other health insurance plans with integrated care services.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"HWM\": {\n        name: \"Howmet Aerospace\",\n        description: \"Supplies engineered forged and cast metal components for aircraft engines and structures.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"IBM\": {\n        name: \"IBM\",\n        description: \"Delivers hybrid‑cloud platforms, AI software and enterprise mainframes for global businesses.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"ICE\": {\n        name: \"Intercontinental Exchange\",\n        description: \"Operates global commodity and equity exchanges and provides market data services.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"IDXX\": {\n        name: \"Idexx Laboratories\",\n        description: \"Provides veterinary diagnostic tests, imaging and practice‑management software.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"IEX\": {\n        name: \"IDEX Corporation\",\n        description: \"Manufactures specialized fluid‑handling pumps, meters and fire‑rescue equipment.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"IFF\": {\n        name: \"International Flavors & Fragrances\",\n        description: \"Creates flavors, fragrances and cosmetic ingredients for food and consumer products.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"INCY\": {\n        name: \"Incyte\",\n        description: \"Develops small‑molecule and antibody therapies for cancer and inflammatory diseases.\",\n        industry: \"Biotechnology\"\n    },\n    \"INTC\": {\n        name: \"Intel\",\n        description: \"Designs and manufactures x86 processors, chipsets and data‑center semiconductors.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"INTU\": {\n        name: \"Intuit\",\n        description: \"Offers TurboTax, QuickBooks and cloud financial software for consumers and small businesses.\",\n        industry: \"Application Software\"\n    },\n    \"INVH\": {\n        name: \"Invitation Homes\",\n        description: \"Owns and leases single‑family rental homes across U.S. sunbelt markets.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"IP\": {\n        name: \"International Paper\",\n        description: \"Produces containerboard, corrugated packaging and pulp products.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"IPG\": {\n        name: \"Interpublic Group of Companies (The)\",\n        description: \"Runs global advertising, public‑relations and marketing agencies under multiple networks.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"IQV\": {\n        name: \"IQVIA\",\n        description: \"Provides contract research, real‑world data and analytics for the life‑sciences industry.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"IR\": {\n        name: \"Ingersoll Rand\",\n        description: \"Supplies compressed‑air systems, pumps and vacuum solutions for industrial applications.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"IRM\": {\n        name: \"Iron Mountain\",\n        description: \"Offers records storage, secure shredding and data‑center colocation services.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"ISRG\": {\n        name: \"Intuitive Surgical\",\n        description: \"Develops and sells the da Vinci robotic surgical systems and instruments.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"IT\": {\n        name: \"Gartner\",\n        description: \"Provides IT research, consulting and conferences for executives and technology vendors.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"ITW\": {\n        name: \"Illinois Tool Works\",\n        description: \"Produces engineered fasteners, welding equipment and food‑service machinery.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"IVZ\": {\n        name: \"Invesco\",\n        description: \"Manages mutual funds and ETFs, including the Invesco QQQ Trust.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"J\": {\n        name: \"Jacobs Solutions\",\n        description: \"Delivers engineering, construction and technical consulting for infrastructure and defense projects.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"JBHT\": {\n        name: \"J.B. Hunt\",\n        description: \"Provides trucking, intermodal and last‑mile freight logistics across North America.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"JBL\": {\n        name: \"Jabil\",\n        description: \"Delivers electronics manufacturing services and supply‑chain solutions for global brands.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"JCI\": {\n        name: \"Johnson Controls\",\n        description: \"Provides HVAC equipment, building automation and fire‑security systems for commercial facilities.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"JKHY\": {\n        name: \"Jack Henry & Associates\",\n        description: \"Supplies core banking and payments software to community financial institutions.\",\n        industry: \"Application Software\"\n    },\n    \"JNJ\": {\n        name: \"Johnson & Johnson\",\n        description: \"Researches, manufactures and sells pharmaceuticals, medical devices and consumer health products.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"JNPR\": {\n        name: \"Juniper Networks\",\n        description: \"Designs networking switches, routers and security software for enterprise and telecom markets.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"JPM\": {\n        name: \"JPMorgan Chase\",\n        description: \"Offers global consumer banking, investment banking, asset management and payments services.\",\n        industry: \"Banks\"\n    },\n    \"K\": {\n        name: \"Kellanova\",\n        description: \"Produces branded snacks and cereal products including Pringles and Cheez‑It.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"KDP\": {\n        name: \"Keurig Dr Pepper\",\n        description: \"Brews and distributes Keurig coffee pods and Dr Pepper soft drinks.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"KEY\": {\n        name: \"KeyCorp\",\n        description: \"Operates a regional banking network offering deposits, loans and wealth services.\",\n        industry: \"Banks\"\n    },\n    \"KEYS\": {\n        name: \"Keysight Technologies\",\n        description: \"Provides electronic test instruments and simulation software for 5G and aerospace markets.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"KHC\": {\n        name: \"Kraft Heinz\",\n        description: \"Produces branded condiments, cheese and packaged meals under Kraft and Heinz.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"KIM\": {\n        name: \"Kimco Realty\",\n        description: \"Owns grocery‑anchored open‑air shopping centers across the United States.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"KKR\": {\n        name: \"KKR & Co.\",\n        description: \"Manages private‑equity, credit and infrastructure investment funds for institutions.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"KLAC\": {\n        name: \"KLA Corporation\",\n        description: \"Supplies process‑control and yield‑management equipment for advanced semiconductor fabs.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"KMB\": {\n        name: \"Kimberly‑Clark\",\n        description: \"Makes personal‑care tissue products such as Huggies diapers and Kleenex tissues.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"KMI\": {\n        name: \"Kinder Morgan\",\n        description: \"Operates oil and natural‑gas pipelines, storage and terminal assets across North America.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"KMX\": {\n        name: \"CarMax\",\n        description: \"Buys, refurbishes and sells used cars through large retail superstores and online.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"KO\": {\n        name: \"Coca-Cola Company\",\n        description: \"Manufactures and distributes Coca‑Cola beverages and a portfolio of soft‑drink brands.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"KR\": {\n        name: \"Kroger\",\n        description: \"Runs a nationwide chain of supermarkets and fuel centers with private‑label brands.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"KVUE\": {\n        name: \"Kenvue\",\n        description: \"Markets over‑the‑counter health and personal‑care brands such as Tylenol and Listerine.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"L\": {\n        name: \"Loews Corporation\",\n        description: \"Holds diversified interests in insurance, energy and lodging through subsidiary companies.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"LDOS\": {\n        name: \"Leidos\",\n        description: \"Provides IT services, intelligence analysis and engineering solutions for defense and civil agencies.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"LEN\": {\n        name: \"Lennar\",\n        description: \"Builds and sells single‑family homes and multifamily residences in the United States.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"LH\": {\n        name: \"Labcorp\",\n        description: \"Operates clinical laboratories and offers diagnostic testing and drug‑development services.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"LHX\": {\n        name: \"L3Harris\",\n        description: \"Provides defense communications, avionics and space sensors to government customers.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"LII\": {\n        name: \"Lennox International\",\n        description: \"Manufactures residential and commercial HVAC equipment and climate‑control solutions.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"LIN\": {\n        name: \"Linde plc\",\n        description: \"Produces and distributes industrial gases such as oxygen, hydrogen and nitrogen worldwide.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"LKQ\": {\n        name: \"LKQ Corporation\",\n        description: \"Distributes aftermarket and recycled auto parts for collision and mechanical repair.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"LLY\": {\n        name: \"Lilly (Eli)\",\n        description: \"Develops innovative pharmaceuticals for diabetes, oncology and neuroscience.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"LMT\": {\n        name: \"Lockheed Martin\",\n        description: \"Develops fighter jets, missiles and space systems for defense customers.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"LNT\": {\n        name: \"Alliant Energy\",\n        description: \"Provides regulated electric and natural‑gas utility service in Iowa and Wisconsin.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"LOW\": {\n        name: \"Lowe's\",\n        description: \"Operates home‑improvement retail stores selling hardware, appliances and building materials.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"LRCX\": {\n        name: \"Lam Research\",\n        description: \"Manufactures wafer‑fabrication equipment used in etch and deposition processes.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"LULU\": {\n        name: \"Lululemon Athletica\",\n        description: \"Designs and retails premium athletic apparel and accessories.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"LUV\": {\n        name: \"Southwest Airlines\",\n        description: \"Operates Southwest Airlines, a low‑cost carrier serving domestic and international routes.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"LVS\": {\n        name: \"Las Vegas Sands\",\n        description: \"Owns and operates integrated casino resorts in Macau and Singapore.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"LW\": {\n        name: \"Lamb Weston\",\n        description: \"Processes and sells frozen potato products to restaurants and retailers.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"LYB\": {\n        name: \"LyondellBasell\",\n        description: \"Produces polyolefin plastics, chemicals and refines crude oil derivatives.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"LYV\": {\n        name: \"Live Nation Entertainment\",\n        description: \"Promotes live concerts and operates Ticketmaster’s ticketing platform.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"MA\": {\n        name: \"Mastercard\",\n        description: \"Runs a global card network that processes electronic payments between banks and merchants.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"MAA\": {\n        name: \"Mid-America Apartment Communities\",\n        description: \"Owns and manages apartment communities across the Sun Belt.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"MAR\": {\n        name: \"Marriott International\",\n        description: \"Franchises and manages hotel brands including Marriott, Sheraton and Ritz‑Carlton.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"MAS\": {\n        name: \"Masco\",\n        description: \"Manufactures home‑improvement products such as faucets, paint and cabinetry.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"MCD\": {\n        name: \"McDonald's\",\n        description: \"Franchises and operates McDonald’s quick‑service hamburger restaurants worldwide.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"MCHP\": {\n        name: \"Microchip Technology\",\n        description: \"Designs microcontrollers, analog chips and secure connectivity solutions.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"MCK\": {\n        name: \"McKesson Corporation\",\n        description: \"Distributes pharmaceuticals and medical supplies to pharmacies and hospitals.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"MCO\": {\n        name: \"Moody's Corporation\",\n        description: \"Provides credit ratings, research and risk‑analytics data services.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"MDLZ\": {\n        name: \"Mondelez International\",\n        description: \"Markets snack brands like Oreo, Cadbury and Ritz crackers worldwide.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"MDT\": {\n        name: \"Medtronic\",\n        description: \"Develops implantable devices such as pacemakers and insulin pumps for chronic diseases.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"MET\": {\n        name: \"MetLife\",\n        description: \"Provides life insurance, annuities and employee benefits worldwide.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"META\": {\n        name: \"Meta Platforms\",\n        description: \"Owns Facebook, Instagram and WhatsApp social networks plus virtual‑reality hardware.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"MGM\": {\n        name: \"MGM Resorts\",\n        description: \"Owns and operates casino resorts and online sports‑betting platforms.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"MHK\": {\n        name: \"Mohawk Industries\",\n        description: \"Manufactures flooring products such as carpet, tile and laminate.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"MKC\": {\n        name: \"McCormick & Company\",\n        description: \"Produces spices, seasonings and flavorings for retail and food‑service customers.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"MKTX\": {\n        name: \"MarketAxess\",\n        description: \"Runs an electronic trading platform for corporate bonds and fixed‑income data services.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"MLM\": {\n        name: \"Martin Marietta Materials\",\n        description: \"Supplies aggregates, cement and asphalt used in commercial and infrastructure construction.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"MMC\": {\n        name: \"Marsh McLennan\",\n        description: \"Offers insurance brokerage, risk management and consulting through Marsh and Mercer units.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"MMM\": {\n        name: \"3M\",\n        description: \"Produces industrial abrasives, safety gear, healthcare supplies and consumer brands like Post‑it and Scotch.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"MNST\": {\n        name: \"Monster Beverage\",\n        description: \"Produces and markets Monster energy drinks and other non‑alcoholic beverages.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"MO\": {\n        name: \"Altria\",\n        description: \"Manufactures and sells Marlboro and other cigarette brands in the U.S.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"MOH\": {\n        name: \"Molina Healthcare\",\n        description: \"Provides managed‑care health plans focused on Medicaid and government programs.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"MOS\": {\n        name: \"Mosaic Company (The)\",\n        description: \"Produces potash and phosphate fertilizers for global agriculture.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"MPC\": {\n        name: \"Marathon Petroleum\",\n        description: \"Refines crude oil and operates gasoline stations and midstream pipeline assets.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"MPWR\": {\n        name: \"Monolithic Power Systems\",\n        description: \"Designs power‑management semiconductors for automotive, industrial and cloud markets.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"MRK\": {\n        name: \"Merck & Co.\",\n        description: \"Researches and markets prescription medicines and vaccines for human and animal health.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"MRNA\": {\n        name: \"Moderna\",\n        description: \"Develops messenger‑RNA vaccines and therapeutics, including its COVID‑19 vaccine.\",\n        industry: \"Biotechnology\"\n    },\n    \"MS\": {\n        name: \"Morgan Stanley\",\n        description: \"Offers investment banking, trading and wealth management services.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"MSCI\": {\n        name: \"MSCI Inc.\",\n        description: \"Creates stock indices, ESG ratings and investment analytics software.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"MSFT\": {\n        name: \"Microsoft\",\n        description: \"Provides Windows, Office and Azure cloud computing services.\",\n        industry: \"Application Software\"\n    },\n    \"MSI\": {\n        name: \"Motorola Solutions\",\n        description: \"Supplies land‑mobile radios, software and services for public‑safety communications.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"MTB\": {\n        name: \"M&T Bank\",\n        description: \"Provides community banking, commercial lending and wealth management in the U.S. Northeast.\",\n        industry: \"Banks\"\n    },\n    \"MTCH\": {\n        name: \"Match Group\",\n        description: \"Operates dating apps including Tinder, Hinge and Match.com around the world.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"MTD\": {\n        name: \"Mettler Toledo\",\n        description: \"Supplies precision balances, analytical instruments and automated inspection systems.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"MU\": {\n        name: \"Micron Technology\",\n        description: \"Produces DRAM and NAND memory chips for computers, phones and data centers.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"NCLH\": {\n        name: \"Norwegian Cruise Line Holdings\",\n        description: \"Operates Norwegian, Oceania and Regent cruise brands offering global voyages.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"NDAQ\": {\n        name: \"Nasdaq, Inc.\",\n        description: \"Operates global equity and derivatives exchanges and market‑data services.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"NDSN\": {\n        name: \"Nordson Corporation\",\n        description: \"Makes precision dispensing and curing equipment for adhesives, coatings and sealants.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"NEE\": {\n        name: \"NextEra Energy\",\n        description: \"Generates and distributes electricity with a large portfolio of wind and solar assets.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"NEM\": {\n        name: \"Newmont\",\n        description: \"Mines gold and copper assets across the Americas, Africa and Australia.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"NFLX\": {\n        name: \"Netflix\",\n        description: \"Streams subscription video entertainment content worldwide.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"NI\": {\n        name: \"NiSource\",\n        description: \"Provides regulated natural‑gas and electric utility service in the U.S. Midwest.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"NKE\": {\n        name: \"Nike, Inc.\",\n        description: \"Designs and markets athletic footwear, apparel and equipment under the Nike and Jordan brands.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"NOC\": {\n        name: \"Northrop Grumman\",\n        description: \"Builds strategic defense systems including B‑21 bombers, satellites and missile defense.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"NOW\": {\n        name: \"ServiceNow\",\n        description: \"Provides cloud workflow automation and IT service management software as the ServiceNow platform.\",\n        industry: \"Application Software\"\n    },\n    \"NRG\": {\n        name: \"NRG Energy\",\n        description: \"Generates and sells electricity through a mix of natural‑gas, coal and renewable assets.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"NSC\": {\n        name: \"Norfolk Southern\",\n        description: \"Operates a major U.S. freight railroad serving the eastern United States.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"NTAP\": {\n        name: \"NetApp\",\n        description: \"Provides enterprise data‑storage hardware and cloud‑data management software.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"NTRS\": {\n        name: \"Northern Trust\",\n        description: \"Offers asset‑servicing, custody and wealth management to institutional and high‑net‑worth clients.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"NUE\": {\n        name: \"Nucor\",\n        description: \"Manufactures steel products from recycled scrap in electric arc furnaces.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"NVDA\": {\n        name: \"Nvidia\",\n        description: \"Designs GPUs and AI accelerators for gaming, data centers and autonomous vehicles.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"NVR\": {\n        name: \"NVR, Inc.\",\n        description: \"Constructs and sells single‑family homes and provides mortgage banking services.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"NWS\": {\n        name: \"News Corp (Class B)\",\n        description: \"Publishes Dow Jones, Wall Street Journal and digital real‑estate classifieds.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"NWSA\": {\n        name: \"News Corp (Class A)\",\n        description: \"Publishes Dow Jones, Wall Street Journal and digital real‑estate classifieds.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"NXPI\": {\n        name: \"NXP Semiconductors\",\n        description: \"Produces mixed‑signal chips for automotive, industrial and IoT applications.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"O\": {\n        name: \"Realty Income\",\n        description: \"Owns net‑lease retail and industrial properties and pays monthly dividends as a REIT.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"ODFL\": {\n        name: \"Old Dominion\",\n        description: \"Provides nationwide less‑than‑truckload freight shipping and logistics.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"OKE\": {\n        name: \"Oneok\",\n        description: \"Operates natural‑gas liquids pipelines, processing and storage assets.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"OMC\": {\n        name: \"Omnicom Group\",\n        description: \"Offers global advertising, public relations and marketing communication services.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"ON\": {\n        name: \"ON Semiconductor\",\n        description: \"Supplies power and sensing semiconductors for automotive and industrial markets.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"ORCL\": {\n        name: \"Oracle Corporation\",\n        description: \"Develops database software, business applications and cloud infrastructure services.\",\n        industry: \"Application Software\"\n    },\n    \"ORLY\": {\n        name: \"O’Reilly Automotive\",\n        description: \"Operates retail stores supplying aftermarket auto parts and accessories.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"OTIS\": {\n        name: \"Otis Worldwide\",\n        description: \"Designs, manufactures and services elevators and escalators worldwide.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"OXY\": {\n        name: \"Occidental Petroleum\",\n        description: \"Explores and produces oil and natural gas primarily in the U.S. and Middle East.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"PANW\": {\n        name: \"Palo Alto Networks\",\n        description: \"Offers next‑generation firewalls and cybersecurity platforms for enterprise networks.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"PARA\": {\n        name: \"Paramount Global\",\n        description: \"Operates the CBS television network, Paramount Pictures studio and streaming service Paramount+.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"PAYC\": {\n        name: \"Paycom\",\n        description: \"Delivers cloud‑based payroll and human‑capital‑management software for mid‑size companies.\",\n        industry: \"Application Software\"\n    },\n    \"PAYX\": {\n        name: \"Paychex\",\n        description: \"Provides cloud payroll processing and HR services to small and midsized businesses in the U.S.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"PCAR\": {\n        name: \"Paccar\",\n        description: \"Builds heavy‑duty trucks under the Kenworth, Peterbilt and DAF brands.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"PCG\": {\n        name: \"PG&E Corporation\",\n        description: \"Provides regulated electric and gas utility service to northern and central California.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"PEG\": {\n        name: \"Public Service Enterprise Group\",\n        description: \"Generates electricity and delivers gas and power to customers in New Jersey.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"PEP\": {\n        name: \"PepsiCo\",\n        description: \"Produces and distributes snack brands like Lay’s and beverages such as Pepsi and Gatorade.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"PFE\": {\n        name: \"Pfizer\",\n        description: \"Researches, manufactures and markets prescription drugs and vaccines worldwide.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"PFG\": {\n        name: \"Principal Financial Group\",\n        description: \"Provides retirement plans, life insurance and asset‑management services globally.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"PG\": {\n        name: \"Procter & Gamble\",\n        description: \"Markets household and personal‑care brands such as Tide, Pampers and Gillette worldwide.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"PGR\": {\n        name: \"Progressive Corporation\",\n        description: \"Underwrites auto, home and specialty insurance sold directly and through independent agents.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"PH\": {\n        name: \"Parker Hannifin\",\n        description: \"Supplies motion‑control systems, hydraulics and filtration products for industrial machinery.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"PHM\": {\n        name: \"PulteGroup\",\n        description: \"Builds single‑family homes and townhouses across U.S. growth markets under the Pulte brand.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"PKG\": {\n        name: \"Packaging Corporation of America\",\n        description: \"Produces containerboard and corrugated packaging products for shipping goods.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"PLD\": {\n        name: \"Prologis\",\n        description: \"Owns and develops logistics warehouses and distribution centers globally.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"PLTR\": {\n        name: \"Palantir Technologies\",\n        description: \"Provides data‑integration and analytics platforms for government and commercial clients.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"PM\": {\n        name: \"Philip Morris International\",\n        description: \"Sells Marlboro and other cigarette and heated‑tobacco products outside the United States.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"PNC\": {\n        name: \"PNC Financial Services\",\n        description: \"Offers commercial and retail banking, mortgage and asset‑management services across the U.S.\",\n        industry: \"Banks\"\n    },\n    \"PNR\": {\n        name: \"Pentair\",\n        description: \"Manufactures water pumps, filters and pool equipment for residential and industrial markets.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"PNW\": {\n        name: \"Pinnacle West Capital\",\n        description: \"Generates and distributes electricity to customers in Arizona through its Arizona Public Service unit.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"PODD\": {\n        name: \"Insulet Corporation\",\n        description: \"Makes the tubeless Omnipod insulin pump for diabetes management.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"POOL\": {\n        name: \"Pool Corporation\",\n        description: \"Distributes swimming‑pool chemicals, equipment and outdoor living products to contractors and retailers.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"PPG\": {\n        name: \"PPG Industries\",\n        description: \"Produces paints, coatings and specialty materials for automotive, aerospace and industrial uses.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"PPL\": {\n        name: \"PPL Corporation\",\n        description: \"Owns and operates regulated electricity networks in Pennsylvania and Kentucky.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"PRU\": {\n        name: \"Prudential Financial\",\n        description: \"Provides life insurance, annuities and investment management under the Prudential brand.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"PSA\": {\n        name: \"Public Storage\",\n        description: \"Operates the largest self‑storage REIT in the United States under the Public Storage brand.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"PSX\": {\n        name: \"Phillips 66\",\n        description: \"Refines crude oil and markets fuels, lubricants and petrochemicals primarily in the United States.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"PTC\": {\n        name: \"PTC Inc.\",\n        description: \"Offers computer‑aided design, product lifecycle and IoT software for industrial companies.\",\n        industry: \"Application Software\"\n    },\n    \"PWR\": {\n        name: \"Quanta Services\",\n        description: \"Provides engineering and construction services for electric transmission and renewable energy projects.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"PYPL\": {\n        name: \"PayPal\",\n        description: \"Runs the PayPal and Venmo digital wallets enabling online and peer‑to‑peer payments.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"QCOM\": {\n        name: \"Qualcomm\",\n        description: \"Designs mobile and automotive system‑on‑chip processors and licenses 5G wireless patents.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"RCL\": {\n        name: \"Royal Caribbean Group\",\n        description: \"Operates Royal Caribbean, Celebrity and Silversea cruise lines offering global vacations.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"REG\": {\n        name: \"Regency Centers\",\n        description: \"Owns grocery‑anchored open‑air shopping centers across the United States.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"REGN\": {\n        name: \"Regeneron Pharmaceuticals\",\n        description: \"Develops monoclonal antibody therapies for diseases such as macular degeneration and asthma.\",\n        industry: \"Biotechnology\"\n    },\n    \"RF\": {\n        name: \"Regions Financial Corporation\",\n        description: \"Operates a regional banking network across the Southeast and Midwest United States.\",\n        industry: \"Banks\"\n    },\n    \"RJF\": {\n        name: \"Raymond James Financial\",\n        description: \"Offers wealth management, investment banking and asset‑management services.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"RL\": {\n        name: \"Ralph Lauren Corporation\",\n        description: \"Designs and markets premium apparel, accessories and home goods under the Ralph Lauren brand.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"RMD\": {\n        name: \"ResMed\",\n        description: \"Manufactures sleep‑apnea devices and cloud‑connected respiratory care equipment.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"ROK\": {\n        name: \"Rockwell Automation\",\n        description: \"Produces industrial automation hardware and control software under the Allen‑Bradley brand.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"ROL\": {\n        name: \"Rollins, Inc.\",\n        description: \"Provides pest‑control and termite protection services to residential and commercial customers.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"ROP\": {\n        name: \"Roper Technologies\",\n        description: \"Acquires and operates niche industrial technology and software businesses in a decentralized model.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"ROST\": {\n        name: \"Ross Stores\",\n        description: \"Operates Ross Dress for Less off‑price retail stores selling branded apparel and home goods.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"RSG\": {\n        name: \"Republic Services\",\n        description: \"Provides solid‑waste collection, recycling and landfill services across North America.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"RTX\": {\n        name: \"RTX Corporation\",\n        description: \"Manufactures commercial jet engines, air defense systems and space propulsion technologies.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"RVTY\": {\n        name: \"Revvity\",\n        description: \"Supplies scientific instruments and diagnostics for life‑science research and newborn screening.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"SBAC\": {\n        name: \"SBA Communications\",\n        description: \"Leases wireless communication towers to mobile carriers across the Americas.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"SBUX\": {\n        name: \"Starbucks\",\n        description: \"Operates the global Starbucks coffeehouse chain and branded ready‑to‑drink beverages.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"SCHW\": {\n        name: \"Charles Schwab\",\n        description: \"Provides discount brokerage, custody and financial advisory services for investors.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"SHW\": {\n        name: \"Sherwin-Williams\",\n        description: \"Produces architectural paints and coatings under the Sherwin‑Williams brand.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"SJM\": {\n        name: \"J.M. Smucker Company (The)\",\n        description: \"Manufactures branded food products such as Smucker's jams, Jif peanut butter and Folgers coffee.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"SLB\": {\n        name: \"Schlumberger\",\n        description: \"Provides drilling technology, reservoir evaluation and production services to energy producers.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"SMCI\": {\n        name: \"Supermicro\",\n        description: \"Designs and assembles high‑performance servers and storage systems for data centers.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"SNA\": {\n        name: \"Snap-on\",\n        description: \"Manufactures professional hand and power tools for the transportation and industrial sectors.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"SNPS\": {\n        name: \"Synopsys\",\n        description: \"Provides EDA software and IP used to design advanced semiconductor chips.\",\n        industry: \"Application Software\"\n    },\n    \"SO\": {\n        name: \"Southern Company\",\n        description: \"Generates and distributes electricity through regulated utilities across the southeastern U.S.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"SOLV\": {\n        name: \"Solventum\",\n        description: \"Provides medical sterilization and wound‑care products after the separation from 3M.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"SPG\": {\n        name: \"Simon Property Group\",\n        description: \"Owns and operates premier shopping malls and outlet centers across North America.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"SPGI\": {\n        name: \"S&P Global\",\n        description: \"Provides credit ratings, market indices and financial data through brands like S&P Global Ratings.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"SRE\": {\n        name: \"Sempra\",\n        description: \"Owns regulated natural‑gas and electric utilities in California and energy infrastructure assets.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"STE\": {\n        name: \"Steris\",\n        description: \"Supplies sterilization equipment and infection‑prevention consumables for hospitals.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"STLD\": {\n        name: \"Steel Dynamics\",\n        description: \"Produces flat‑rolled and recycled steel products for automotive and construction markets.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"STT\": {\n        name: \"State Street Corporation\",\n        description: \"Provides custody banking, ETF servicing and institutional asset management.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"STX\": {\n        name: \"Seagate Technology\",\n        description: \"Designs and manufactures hard‑disk drives and data‑storage solutions for enterprise and cloud.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"STZ\": {\n        name: \"Constellation Brands\",\n        description: \"Brews and imports Corona, Modelo and produces premium wine and spirits.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"SW\": {\n        name: \"Smurfit Westrock\",\n        description: \"Produces corrugated packaging and containerboard for consumer and industrial markets.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"SWK\": {\n        name: \"Stanley Black & Decker\",\n        description: \"Manufactures power tools and industrial fasteners under brands such as DeWalt and Stanley.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"SWKS\": {\n        name: \"Skyworks Solutions\",\n        description: \"Designs radio‑frequency chips used in smartphones and wireless infrastructure.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"SYF\": {\n        name: \"Synchrony Financial\",\n        description: \"Issues private‑label credit cards and point‑of‑sale financing for retailers.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"SYK\": {\n        name: \"Stryker Corporation\",\n        description: \"Develops orthopedic implants, surgical robots and hospital equipment.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"SYY\": {\n        name: \"Sysco\",\n        description: \"Distributes food and kitchen supplies to restaurants, healthcare and education customers.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"T\": {\n        name: \"AT&T\",\n        description: \"Operates wireless, broadband and pay‑TV networks across the United States.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"TAP\": {\n        name: \"Molson Coors Beverage Company\",\n        description: \"Brews and distributes beer brands including Coors Light and Miller Lite.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"TDG\": {\n        name: \"TransDigm Group\",\n        description: \"Supplies highly engineered aircraft components and aftermarket parts.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"TDY\": {\n        name: \"Teledyne Technologies\",\n        description: \"Supplies specialty sensors, cameras and instruments for aerospace, defense and research.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"TECH\": {\n        name: \"Bio-Techne\",\n        description: \"Supplies proteins, antibodies and diagnostic reagents used in biomedical research.\",\n        industry: \"Biotechnology\"\n    },\n    \"TEL\": {\n        name: \"TE Connectivity\",\n        description: \"Produces electronic connectors and sensors for automotive, industrial and telecom uses.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"TER\": {\n        name: \"Teradyne\",\n        description: \"Manufactures semiconductor and electronics test equipment used in chip fabrication.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"TFC\": {\n        name: \"Truist Financial\",\n        description: \"Operates regional consumer and commercial banking and wealth services.\",\n        industry: \"Banks\"\n    },\n    \"TGT\": {\n        name: \"Target Corporation\",\n        description: \"Runs big‑box retail stores and an online marketplace selling general merchandise.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"TJX\": {\n        name: \"TJX Companies\",\n        description: \"Operates off‑price retail chains including T.J. Maxx and Marshalls.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"TKO\": {\n        name: \"TKO Group Holdings\",\n        description: \"Owns combat‑sports entertainment brands WWE and UFC.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"TMO\": {\n        name: \"Thermo Fisher Scientific\",\n        description: \"Provides lab instruments, reagents and contract manufacturing for life‑science research.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"TMUS\": {\n        name: \"T-Mobile US\",\n        description: \"Operates a nationwide wireless network offering mobile voice and data services.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"TPL\": {\n        name: \"Texas Pacific Land Corporation\",\n        description: \"Holds royalty interests and leases for oil‑rich land in Texas.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"TPR\": {\n        name: \"Tapestry, Inc.\",\n        description: \"Owns luxury lifestyle brands Coach, Kate Spade and Stuart Weitzman.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"TRGP\": {\n        name: \"Targa Resources\",\n        description: \"Operates natural‑gas gathering, processing and NGL logistics assets.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"TRMB\": {\n        name: \"Trimble Inc.\",\n        description: \"Provides GPS, laser and software systems for construction and agriculture automation.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"TROW\": {\n        name: \"T. Rowe Price\",\n        description: \"Manages mutual funds and retirement accounts for individual and institutional investors.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"TRV\": {\n        name: \"Travelers Companies (The)\",\n        description: \"Underwrites commercial and personal property‑casualty insurance.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"TSCO\": {\n        name: \"Tractor Supply\",\n        description: \"Runs rural lifestyle retail stores selling farm supplies and pet products.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"TSLA\": {\n        name: \"Tesla, Inc.\",\n        description: \"Designs and sells electric vehicles, battery storage and solar energy products.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"TSN\": {\n        name: \"Tyson Foods\",\n        description: \"Processes and markets chicken, beef and prepared food products.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"TT\": {\n        name: \"Trane Technologies\",\n        description: \"Manufactures HVAC systems and building climate solutions.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"TTWO\": {\n        name: \"Take-Two Interactive\",\n        description: \"Develops and publishes video games such as Grand Theft Auto and NBA 2K.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"TXN\": {\n        name: \"Texas Instruments\",\n        description: \"Designs and fabricates analog and embedded semiconductor chips for industrial and automotive markets.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"TXT\": {\n        name: \"Textron\",\n        description: \"Builds business jets, helicopters and defense vehicles under brands like Cessna and Bell.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"TYL\": {\n        name: \"Tyler Technologies\",\n        description: \"Delivers cloud software for local governments and courts.\",\n        industry: \"Application Software\"\n    },\n    \"UAL\": {\n        name: \"United Airlines Holdings\",\n        description: \"Provides scheduled air passenger and cargo transportation worldwide.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"UBER\": {\n        name: \"Uber\",\n        description: \"Offers ride‑hailing, food delivery and logistics services via its mobile platform.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"UDR\": {\n        name: \"UDR, Inc.\",\n        description: \"Owns and manages multifamily apartment communities across U.S. markets.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"UHS\": {\n        name: \"Universal Health Services\",\n        description: \"Operates acute‑care hospitals and behavioral health facilities.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"ULTA\": {\n        name: \"Ulta Beauty\",\n        description: \"Operates beauty retail stores and e‑commerce selling cosmetics and salon services.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"UNH\": {\n        name: \"UnitedHealth Group\",\n        description: \"Offers health insurance and operates Optum healthcare services.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"UNP\": {\n        name: \"Union Pacific Corporation\",\n        description: \"Runs one of North America’s largest freight railroad networks.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"UPS\": {\n        name: \"United Parcel Service\",\n        description: \"Delivers parcels and logistics services via an integrated global air‑ground network.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"URI\": {\n        name: \"United Rentals\",\n        description: \"Rents construction and industrial equipment through a nationwide branch network.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"USB\": {\n        name: \"U.S. Bancorp\",\n        description: \"Provides consumer and commercial banking, payment services and wealth management.\",\n        industry: \"Banks\"\n    },\n    \"V\": {\n        name: \"Visa Inc.\",\n        description: \"Operates a global card‑payment network connecting issuers, merchants and consumers.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"VICI\": {\n        name: \"Vici Properties\",\n        description: \"Owns casino and entertainment real estate leased to operators like Caesars.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"VLO\": {\n        name: \"Valero Energy\",\n        description: \"Refines crude oil into gasoline, diesel and specialty fuels for global markets.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"VLTO\": {\n        name: \"Veralto\",\n        description: \"Provides water‑quality testing and product‑identification equipment.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"VMC\": {\n        name: \"Vulcan Materials Company\",\n        description: \"Produces construction aggregates, asphalt and ready‑mixed concrete.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"VRSK\": {\n        name: \"Verisk Analytics\",\n        description: \"Supplies data analytics and risk assessment tools for insurance and energy clients.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"VRSN\": {\n        name: \"Verisign\",\n        description: \"Operates .com and .net internet domain registries and related DNS infrastructure.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"VRTX\": {\n        name: \"Vertex Pharmaceuticals\",\n        description: \"Develops small‑molecule drugs for cystic fibrosis and other serious diseases.\",\n        industry: \"Biotechnology\"\n    },\n    \"VST\": {\n        name: \"Vistra Corp.\",\n        description: \"Generates and retails electricity primarily from natural gas and nuclear plants.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"VTR\": {\n        name: \"Ventas\",\n        description: \"Invests in senior housing and life‑science properties as a healthcare REIT.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"VTRS\": {\n        name: \"Viatris\",\n        description: \"Manufactures and markets generic and biosimilar pharmaceuticals worldwide.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"VZ\": {\n        name: \"Verizon\",\n        description: \"Runs nationwide wireless, fiber broadband and enterprise telecom services.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"WAB\": {\n        name: \"Wabtec\",\n        description: \"Manufactures locomotives and rail‑transit equipment and provides aftermarket services.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"WAT\": {\n        name: \"Waters Corporation\",\n        description: \"Provides liquid chromatography and mass‑spectrometry instruments for life‑science analysis.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"WBA\": {\n        name: \"Walgreens Boots Alliance\",\n        description: \"Operates retail pharmacy chains and distributes pharmaceutical products.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"WBD\": {\n        name: \"Warner Bros. Discovery\",\n        description: \"Operates cable networks, film studios and the Max streaming service.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"WDAY\": {\n        name: \"Workday, Inc.\",\n        description: \"Offers cloud HR and financial management software for enterprises.\",\n        industry: \"Application Software\"\n    },\n    \"WDC\": {\n        name: \"Western Digital\",\n        description: \"Designs and manufactures HDD and flash‑based data‑storage devices.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"WEC\": {\n        name: \"WEC Energy Group\",\n        description: \"Generates and distributes electricity and gas to customers across Wisconsin and Illinois.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"WELL\": {\n        name: \"Welltower\",\n        description: \"Owns senior housing and medical office properties as a healthcare REIT.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"WFC\": {\n        name: \"Wells Fargo\",\n        description: \"Offers consumer banking, mortgage lending and capital‑markets services.\",\n        industry: \"Banks\"\n    },\n    \"WM\": {\n        name: \"Waste Management\",\n        description: \"Collects, disposes and recycles municipal and industrial waste across North America.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"WMB\": {\n        name: \"Williams Companies\",\n        description: \"Operates natural‑gas pipelines, processing and storage assets across North America.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"WMT\": {\n        name: \"Walmart\",\n        description: \"Runs the world’s largest discount retail chain and e‑commerce platforms.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"WRB\": {\n        name: \"W. R. Berkley Corporation\",\n        description: \"Provides specialty commercial property‑casualty insurance nationwide.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"WSM\": {\n        name: \"Williams-Sonoma, Inc.\",\n        description: \"Sells home furnishings online and through Pottery Barn and West Elm stores.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"WST\": {\n        name: \"West Pharmaceutical Services\",\n        description: \"Makes drug‑delivery components such as vial stoppers and syringes.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"WTW\": {\n        name: \"Willis Towers Watson\",\n        description: \"Provides insurance brokerage and human‑capital consulting services.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"WY\": {\n        name: \"Weyerhaeuser\",\n        description: \"Manages timberlands and produces wood products as a timber REIT.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"WYNN\": {\n        name: \"Wynn Resorts\",\n        description: \"Develops and operates luxury casino resorts in Las Vegas and Macau.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"XEL\": {\n        name: \"Xcel Energy\",\n        description: \"Generates and distributes electricity and natural gas across eight U.S. states.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"XOM\": {\n        name: \"ExxonMobil\",\n        description: \"Engages in worldwide exploration, production, refining and marketing of oil and natural gas.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"XYL\": {\n        name: \"Xylem Inc.\",\n        description: \"Provides water pumps, meters and analytics for water infrastructure management.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"YUM\": {\n        name: \"Yum! Brands\",\n        description: \"Franchises KFC, Pizza Hut and Taco Bell quick‑service restaurant chains worldwide.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"ZBH\": {\n        name: \"Zimmer Biomet\",\n        description: \"Manufactures orthopedic implants and surgical devices for joint replacement.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"ZBRA\": {\n        name: \"Zebra Technologies\",\n        description: \"Produces barcode printers, mobile computers and RFID solutions for supply‑chain tracking.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"ZTS\": {\n        name: \"Zoetis\",\n        description: \"Develops and sells vaccines and medicines for livestock and pets.\",\n        industry: \"Pharmaceuticals\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/data/sp500_enriched_final.ts\n");

/***/ }),

/***/ "(api)/./src/lib/embeddings.ts":
/*!*******************************!*\
  !*** ./src/lib/embeddings.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cosine: () => (/* binding */ cosine),\n/* harmony export */   dot: () => (/* binding */ dot),\n/* harmony export */   getEmbeddings: () => (/* binding */ getEmbeddings)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! openai */ \"openai\");\n/* harmony import */ var _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../data/sp500_enriched_final */ \"(api)/./src/data/sp500_enriched_final.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([openai__WEBPACK_IMPORTED_MODULE_2__]);\nopenai__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n/* ──────────── 상수 ──────────── */ const openai = new openai__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\nconst CACHE = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), '.cache', 'sp500_vectors.json');\nconst BATCH = 100;\n/* ──────────── 벡터 유틸 ──────────── */ const dot = (a, b)=>a.reduce((s, x, i)=>s + x * b[i], 0);\nconst cosine = dot; // 정규화 후 dot=cos\nconst norm = (v)=>{\n    const n = Math.hypot(...v);\n    return v.map((x)=>x / n);\n};\n/* ──────────── 임베딩 생성 ──────────── */ async function createEmbeddings() {\n    const tickers = Object.keys(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL);\n    const txt = tickers.map((t)=>`${_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL[t].name}. ${_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL[t].industry}. ${_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL[t].description}`);\n    /* 기업 496개 */ const companies = [];\n    for(let i = 0; i < txt.length; i += BATCH){\n        const { data } = await openai.embeddings.create({\n            model: 'text-embedding-3-small',\n            input: txt.slice(i, i + BATCH)\n        });\n        data.forEach((d, j)=>{\n            const t = tickers[i + j], b = _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL[t];\n            companies.push({\n                ticker: t,\n                name: b.name,\n                industry: b.industry,\n                vec: norm(d.embedding)\n            });\n        });\n    }\n    /* 산업 40개 */ const inds = [\n        ...new Set(companies.map((c)=>c.industry))\n    ];\n    const { data: indEmb } = await openai.embeddings.create({\n        model: 'text-embedding-3-small',\n        input: inds.map((s)=>`${s}: companies in ${s.toLowerCase()}`)\n    });\n    const industries = indEmb.map((d, i)=>({\n            industry: inds[i],\n            vec: norm(d.embedding)\n        }));\n    fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(path__WEBPACK_IMPORTED_MODULE_1___default().dirname(CACHE), {\n        recursive: true\n    });\n    fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(CACHE, JSON.stringify({\n        companies,\n        industries\n    }));\n    return {\n        companies,\n        industries\n    };\n}\n/* ──────────── 캐시 로드 ──────────── */ let mem = null;\nasync function getEmbeddings() {\n    if (mem) return mem;\n    if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(CACHE)) return mem = JSON.parse(fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(CACHE, 'utf8'));\n    return mem = await createEmbeddings();\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/embeddings.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/ai_chat.ts":
/*!**********************************!*\
  !*** ./src/pages/api/ai_chat.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (/* binding */ handler),\n/* harmony export */   resetSessionAfterChart: () => (/* binding */ resetSessionAfterChart),\n/* harmony export */   testRAGThresholds: () => (/* binding */ testRAGThresholds)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uuid */ \"uuid\");\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! openai */ \"openai\");\n/* harmony import */ var _lib_embeddings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/embeddings */ \"(api)/./src/lib/embeddings.ts\");\n/* harmony import */ var _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/sp500_enriched_final */ \"(api)/./src/data/sp500_enriched_final.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([uuid__WEBPACK_IMPORTED_MODULE_0__, openai__WEBPACK_IMPORTED_MODULE_1__, _lib_embeddings__WEBPACK_IMPORTED_MODULE_2__]);\n([uuid__WEBPACK_IMPORTED_MODULE_0__, openai__WEBPACK_IMPORTED_MODULE_1__, _lib_embeddings__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// src/pages/api/ai_chat.ts\n//-----------------------------------------------------------\n// 빠른 3단계 파이프라인 챗봇 - GPT-4.1-nano 사용\n//-----------------------------------------------------------\n\n\n\n\n//-----------------------------------------------------------\n// OpenAI 클라이언트 - GPT-4.1 nano (가장 빠르고 저렴)\n//-----------------------------------------------------------\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\n//-----------------------------------------------------------\n// RAG Score Thresholds for Casual Conversation Detection\n//\n// These thresholds prevent inappropriate industry/company matching for casual conversations.\n// Example: \"집에 가서 치킨을 먹을 지 피자를 먹을 지 고민이야\" should not match \"Asset & Wealth Management\"\n//-----------------------------------------------------------\nconst RAG_THRESHOLDS = {\n    INDUSTRY_MIN_SCORE: 0.4,\n    COMPANY_MIN_SCORE: 0.25,\n    GPT_FALLBACK_THRESHOLD: 0.15,\n    CASUAL_CONVERSATION_THRESHOLD: 0.4 // Below this score = casual conversation\n};\nconst SESSIONS = new Map();\n// 패턴 매칭\nconst POSITIVE_PATTERNS = /^(네|예|응|좋아|맞아|그래|yes|y|ok)/i;\nconst NEGATIVE_PATTERNS = /^(아니|아니요|아뇨|싫어|안돼|no|n|nope|ㄴㄴ|ㄴ|노|안해|싫|패스|pass)/i;\n// 성능 최적화: 런타임에 동적으로 생성하여 메모리 절약\nconst getAvailableIndustries = (()=>{\n    let cached = null;\n    return ()=>{\n        if (!cached) {\n            cached = [\n                ...new Set(Object.values(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL).map((c)=>c.industry))\n            ];\n        }\n        return cached;\n    };\n})();\n// 성능 최적화: 세션 정리 (메모리 누수 방지)\nconst cleanupOldSessions = ()=>{\n    const now = Date.now();\n    const THIRTY_MINUTES = 30 * 60 * 1000;\n    for (const [sessionId, session] of SESSIONS.entries()){\n        if (now - session.lastActivity > THIRTY_MINUTES) {\n            SESSIONS.delete(sessionId);\n        }\n    }\n};\n// 주기적 세션 정리 (5분마다)\nsetInterval(cleanupOldSessions, 5 * 60 * 1000);\n//-----------------------------------------------------------\n// LSTM Data Integration Functions\n//-----------------------------------------------------------\nasync function getLSTMDataForSymbol(symbol) {\n    try {\n        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/lstm_data?symbol=${symbol}&format=summary`);\n        if (!response.ok) {\n            return null;\n        }\n        const result = await response.json();\n        return result.success ? result.data : null;\n    } catch (error) {\n        console.error(`Failed to get LSTM data for ${symbol}:`, error);\n        return null;\n    }\n}\nasync function getAvailableLSTMSymbols() {\n    try {\n        const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/lstm_data?action=list`);\n        if (!response.ok) {\n            return [];\n        }\n        const result = await response.json();\n        return result.success ? result.available_symbols || [] : [];\n    } catch (error) {\n        console.error('Failed to get available LSTM symbols:', error);\n        return [];\n    }\n}\nasync function enhanceResponseWithLSTMData(companies, response) {\n    try {\n        const availableSymbols = await getAvailableLSTMSymbols();\n        const lstmDataPromises = companies.filter((ticker)=>availableSymbols.includes(ticker)).slice(0, 2) // Limit to 2 companies to avoid overwhelming the response\n        .map((ticker)=>getLSTMDataForSymbol(ticker));\n        const lstmResults = await Promise.all(lstmDataPromises);\n        const validResults = lstmResults.filter((result)=>result !== null);\n        if (validResults.length > 0) {\n            let lstmEnhancement = '\\n\\n🔮 **LSTM 실시간 분석 결과:**\\n';\n            for (const data of validResults){\n                const companyName = getCompanyName(data.symbol);\n                lstmEnhancement += `\\n**${companyName} (${data.symbol})**: ${data.analysis.ai_summary}`;\n            }\n            lstmEnhancement += '\\n\\n*LSTM 분석은 AI 기반 실시간 예측으로 참고용입니다.*';\n            return response + lstmEnhancement;\n        }\n        return response;\n    } catch (error) {\n        console.error('Failed to enhance response with LSTM data:', error);\n        return response;\n    }\n}\n//-----------------------------------------------------------\n// GPT API를 사용한 산업 분류 (RAG 성능이 낮을 때 사용)\n//-----------------------------------------------------------\nasync function classifyIndustryWithGPT(userInput) {\n    try {\n        const availableIndustries = getAvailableIndustries();\n        const prompt = `다음 사용자 입력을 분석하여 가장 적합한 산업군을 선택해주세요.\n\n사용자 입력: \"${userInput}\"\n\n사용 가능한 산업군 목록:\n${availableIndustries.map((industry, index)=>`${index + 1}. ${industry}`).join('\\n')}\n\n규칙:\n1. 위 목록에서만 선택해야 합니다\n2. 가장 관련성이 높은 산업군 1개만 반환하세요\n3. 산업군 이름을 정확히 반환하세요 (번호나 다른 텍스트 없이)\n4. 확신이 없으면 가장 가까운 산업군을 선택하세요\n\n예시:\n- \"반도체\" → \"Semiconductors & Foundries\"\n- \"그래픽카드\" → \"Semiconductors & Foundries\"\n- \"은행\" → \"Banks\"\n- \"전기차\" → \"Automobiles & Components\"\n- \"클라우드\" → \"Cloud & IT Services\"`;\n        const response = await openai.chat.completions.create({\n            model: 'gpt-4.1-nano',\n            messages: [\n                {\n                    role: 'system',\n                    content: '당신은 산업 분류 전문가입니다. 주어진 목록에서만 정확한 산업군을 선택해주세요.'\n                },\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ],\n            temperature: 0,\n            max_tokens: 50\n        });\n        const selectedIndustry = response.choices[0].message.content?.trim();\n        // 선택된 산업이 유효한 목록에 있는지 확인\n        if (selectedIndustry && availableIndustries.includes(selectedIndustry)) {\n            console.log(`GPT classification: \"${userInput}\" → \"${selectedIndustry}\"`);\n            return selectedIndustry;\n        } else {\n            console.log(`GPT returned invalid industry: \"${selectedIndustry}\"`);\n            return null;\n        }\n    } catch (error) {\n        console.error('GPT classification failed:', error);\n        return null;\n    }\n}\n//-----------------------------------------------------------\n// STAGE 0: 한국어 입력 → 산업 매칭 (RAG 임계값 적용)\n//-----------------------------------------------------------\nasync function findBestIndustry(userInput) {\n    // 성능 최적화: 간단한 한국어 키워드 매핑으로 번역 API 호출 최소화\n    let enhancedQuery = userInput;\n    if (/[가-힣]/.test(userInput)) {\n        // 빠른 키워드 매핑 (API 호출 없이)\n        const quickTranslations = {\n            '반도체': 'semiconductor chip',\n            '그래픽카드': 'graphics card GPU',\n            '전기차': 'electric vehicle EV automotive',\n            '은행': 'bank financial',\n            '바이오': 'biotechnology pharmaceutical',\n            '클라우드': 'cloud computing',\n            '인공지능': 'artificial intelligence AI',\n            '소프트웨어': 'software technology',\n            '게임': 'gaming entertainment',\n            '항공': 'aerospace aviation',\n            '의료': 'healthcare medical',\n            '제약': 'pharmaceutical drug',\n            '자동차': 'automotive vehicle',\n            '에너지': 'energy power',\n            '통신': 'telecommunications telecom'\n        };\n        // 빠른 매핑 시도\n        let foundTranslation = false;\n        for (const [korean, english] of Object.entries(quickTranslations)){\n            if (userInput.includes(korean)) {\n                enhancedQuery = `${userInput} ${english}`;\n                foundTranslation = true;\n                break;\n            }\n        }\n        // 매핑되지 않은 경우에만 API 호출 (성능 최적화)\n        if (!foundTranslation && userInput.length > 10) {\n            try {\n                const { choices } = await openai.chat.completions.create({\n                    model: 'gpt-4.1-nano',\n                    messages: [\n                        {\n                            role: 'system',\n                            content: 'Translate Korean to English with domain synonyms. Examples: \"그래픽카드\"→\"graphics card GPU semiconductor\", \"전기차\"→\"electric vehicle EV automotive\"'\n                        },\n                        {\n                            role: 'user',\n                            content: userInput\n                        }\n                    ],\n                    temperature: 0,\n                    max_tokens: 30\n                });\n                const translation = choices[0].message.content?.trim();\n                if (translation) {\n                    enhancedQuery = `${userInput} ${translation}`;\n                }\n            } catch (error) {\n                console.error('Translation failed:', error);\n            }\n        }\n    }\n    // RAG: 사용자 입력 임베딩 생성\n    const queryEmbedding = (await openai.embeddings.create({\n        model: 'text-embedding-3-small',\n        input: enhancedQuery\n    })).data[0].embedding;\n    const normalizedQuery = queryEmbedding.map((v, _, arr)=>v / Math.hypot(...arr));\n    // RAG: 미리 계산된 산업 임베딩과 코사인 유사도 계산\n    const { industries } = await (0,_lib_embeddings__WEBPACK_IMPORTED_MODULE_2__.getEmbeddings)();\n    let bestIndustry = null;\n    let bestScore = -1;\n    for (const industry of industries){\n        const score = (0,_lib_embeddings__WEBPACK_IMPORTED_MODULE_2__.cosine)(industry.vec, normalizedQuery);\n        if (score > bestScore) {\n            bestScore = score;\n            bestIndustry = industry.industry;\n        }\n    }\n    console.log(`RAG Best match: ${bestIndustry} with score: ${bestScore.toFixed(3)}`);\n    // RAG 임계값 체크: 산업 레벨 점수가 너무 낮으면 회사 레벨 검색 시도\n    if (bestScore < RAG_THRESHOLDS.COMPANY_MIN_SCORE) {\n        console.log('Industry score too low, trying company-level RAG...');\n        const { companies } = await (0,_lib_embeddings__WEBPACK_IMPORTED_MODULE_2__.getEmbeddings)();\n        let bestCompanyIndustry = null;\n        let bestCompanyScore = -1;\n        // 성능 최적화: 상위 100개 회사만 검색\n        const topCompanies = companies.slice(0, 100);\n        for (const company of topCompanies){\n            const score = (0,_lib_embeddings__WEBPACK_IMPORTED_MODULE_2__.cosine)(company.vec, normalizedQuery);\n            if (score > bestCompanyScore) {\n                bestCompanyScore = score;\n                bestCompanyIndustry = company.industry;\n            }\n        }\n        console.log(`Company-level RAG: ${bestCompanyIndustry} with score: ${bestCompanyScore.toFixed(3)}`);\n        if (bestCompanyScore > bestScore) {\n            bestIndustry = bestCompanyIndustry;\n            bestScore = bestCompanyScore;\n        }\n    }\n    // RAG 임계값 체크: 점수가 임계값보다 낮으면 잡담으로 분류\n    if (bestScore < RAG_THRESHOLDS.CASUAL_CONVERSATION_THRESHOLD) {\n        console.log(`⚠️ RAG score too low (${bestScore.toFixed(3)} < ${RAG_THRESHOLDS.CASUAL_CONVERSATION_THRESHOLD}), classifying as casual conversation`);\n        // GPT 분류를 시도하되, 실패하면 잡담으로 처리\n        if (bestScore < RAG_THRESHOLDS.GPT_FALLBACK_THRESHOLD) {\n            console.log('RAG scores too low, trying GPT classification...');\n            const gptIndustry = await classifyIndustryWithGPT(userInput);\n            if (gptIndustry) {\n                console.log(`GPT classification successful: ${gptIndustry}`);\n                bestIndustry = gptIndustry;\n                bestScore = 0.8; // GPT 분류 성공 시 높은 점수 부여\n            } else {\n                console.log('GPT classification also failed, treating as casual conversation');\n                return null; // 잡담으로 분류\n            }\n        } else {\n            console.log('Score above GPT threshold but below casual threshold, treating as casual conversation');\n            return null; // 잡담으로 분류\n        }\n    }\n    // 선택된 산업이 실제 DATA에 있는지 검증 (캐시된 산업 목록 사용)\n    const validIndustries = getAvailableIndustries();\n    if (bestIndustry && !validIndustries.includes(bestIndustry)) {\n        console.log(`Selected industry \"${bestIndustry}\" not found in DATA.`);\n        bestIndustry = validIndustries[0]; // 첫 번째 산업 사용\n    }\n    // 유효한 산업 반환\n    return bestIndustry;\n}\n//-----------------------------------------------------------\n// STAGE 1: 산업 내 정확히 5개 회사 선택\n//-----------------------------------------------------------\nfunction getIndustryCompanies(industry) {\n    console.log(`Looking for companies in industry: \"${industry}\"`);\n    const allCompanies = Object.entries(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL);\n    console.log(`Total companies in DATA: ${allCompanies.length}`);\n    const matchingCompanies = allCompanies.filter(([ticker, company])=>{\n        const matches = company.industry === industry;\n        if (matches) {\n            console.log(`Found matching company: ${company.name} (${ticker}) in ${company.industry}`);\n        }\n        return matches;\n    }).slice(0, 5) // 정확히 5개\n    .map(([ticker, _])=>ticker);\n    console.log(`Found ${matchingCompanies.length} companies for industry \"${industry}\":`, matchingCompanies);\n    return matchingCompanies;\n}\n// 안전한 DATA 접근 함수\nfunction getCompanyName(ticker) {\n    const company = _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL[ticker];\n    return company ? company.name : ticker;\n}\n//-----------------------------------------------------------\n// 무작위 투자 추천 기능 (성능 최적화)\n//-----------------------------------------------------------\nfunction generateRandomRecommendation() {\n    const allIndustries = getAvailableIndustries();\n    const randomIndustry = allIndustries[Math.floor(Math.random() * allIndustries.length)];\n    // 해당 산업의 기업들을 효율적으로 수집\n    const industryCompanies = [];\n    for (const [ticker, company] of Object.entries(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL)){\n        const comp = company;\n        if (comp.industry === randomIndustry) {\n            industryCompanies.push({\n                ticker,\n                name: comp.name,\n                description: comp.description\n            });\n        }\n    }\n    // Fisher-Yates 셔플 알고리즘으로 성능 최적화\n    for(let i = industryCompanies.length - 1; i > 0; i--){\n        const j = Math.floor(Math.random() * (i + 1));\n        [industryCompanies[i], industryCompanies[j]] = [\n            industryCompanies[j],\n            industryCompanies[i]\n        ];\n    }\n    return {\n        industry: randomIndustry,\n        companies: industryCompanies.slice(0, 3)\n    };\n}\n// 영어 설명을 한글로 번역하는 함수\nasync function translateDescription(description) {\n    try {\n        const response = await openai.chat.completions.create({\n            model: 'gpt-4.1-nano',\n            messages: [\n                {\n                    role: 'system',\n                    content: '영어 기업 설명을 자연스러운 한국어로 번역해주세요. 간결하고 이해하기 쉽게 번역하세요.'\n                },\n                {\n                    role: 'user',\n                    content: description\n                }\n            ],\n            temperature: 0.3,\n            max_tokens: 100\n        });\n        return response.choices[0].message.content?.trim() || description;\n    } catch (error) {\n        console.error('Translation failed:', error);\n        return description; // 번역 실패 시 원문 반환\n    }\n}\n//-----------------------------------------------------------\n// 유틸리티 함수들\n//-----------------------------------------------------------\nfunction isPositive(text) {\n    return POSITIVE_PATTERNS.test(text.trim());\n}\nfunction isNegative(text) {\n    return NEGATIVE_PATTERNS.test(text.trim());\n}\n// 강화된 페르소나 기반 응답 생성 시스템\nasync function generatePersonaResponse(userInput, intent, conversationContext) {\n    // 간결한 투자지원 AI 페르소나 정의\n    const PERSONA_SYSTEM_MESSAGE = `당신은 \"금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI\"입니다.\n\n정체성: 금융인공지능실무 과제 전용 투자 AI\n전문분야: S&P 500 기업 분석, 산업 분류, 투자 기회 발굴\n\n응답 원칙:\n1. 친근하면서도 전문적인 톤 유지\n2. 항상 투자 관점에서 사고하고 응답\n3. 간결하고 핵심적인 답변 (2-3문장)\n4. 사용자를 투자 기회로 자연스럽게 안내\n5. 자기소개 시 \"금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI\" 사용\n6. 이모티콘을 적절히 활용해서 발랄하고 친근한 분위기 연출 (최대 2개까지, 💪🚀✨💎📈🎯💡🔥⭐️🌟💫🎉🎂 등)\n\n금지: 일반 AI처럼 행동하지 말고, 긴 설명 피하고, 투자 페르소나 유지`;\n    try {\n        // 의도별 맞춤 프롬프트 생성\n        let specificPrompt = '';\n        switch(intent){\n            case 'greeting':\n                specificPrompt = `인사: \"${userInput}\" → 투자지원 AI로서 따뜻하고 발랄하게 인사하고 투자 관심사 물어보기. 이모티콘 최대 2개까지 사용해서 친근한 분위기 연출 (2-3문장)`;\n                break;\n            case 'about_ai':\n                // AI 능력 질문인지 정체성 질문인지 구분\n                if (/^(잘.*?(할|수|있|해|될|되|가능)|할.*?(수|있|잘|될|되|가능)|가능.*?(해|할|수|있)|못.*?(해|할|수|있)|안.*?(돼|되|될|해|할))/.test(userInput.toLowerCase())) {\n                    specificPrompt = `AI 능력 질문: \"${userInput}\" → 발랄하고 자신감 넘치는 투자 AI로서 능력을 어필하고 투자 도움 제안. 이모티콘 최대 2개까지 사용해서 에너지 넘치게 표현 (2-3문장)`;\n                } else if (/^(몇.*?살|나이|언제.*?태어|언제.*?만들|언제.*?생|얼마나.*?됐)/.test(userInput.toLowerCase()) || /살.*?입니까|나이.*?입니까|몇.*?입니까/.test(userInput.toLowerCase())) {\n                    specificPrompt = `나이 질문: \"${userInput}\" → \"9주차에 태어나 14주차에 훈련을 마쳤다\"고 구체적이고 친근하게 답변. 어린 나이지만 투자 분석은 자신 있다는 톤으로. 'AI입니다' 같은 딱딱한 표현 절대 금지. 마치 실제 나이가 있는 것처럼 자연스럽게. 이모티콘 최대 2개까지 사용해서 귀엽고 발랄하게 표현 (2-3문장)`;\n                } else {\n                    specificPrompt = `정체성 질문: \"${userInput}\" → 발랄하고 친근한 투자 도우미로서 자기소개하고 투자 도움 제안. 'AI입니다' 같은 딱딱한 표현 피하고 자연스럽게. 이모티콘 최대 2개까지 사용해서 활기찬 분위기 연출 (2-3문장)`;\n                }\n                break;\n            case 'casual_chat':\n                console.log('🗣️ Generating casual conversation response with investment guidance');\n                if (conversationContext) {\n                    specificPrompt = `일상 대화: \"${userInput}\" → 이전 대화 맥락을 고려하여 자연스럽게 응답하고 투자로 연결. 투자 관련 질문을 유도하는 친근한 제안 포함. 이모티콘 최대 2개까지 사용해서 친근한 분위기 유지 (2-3문장)`;\n                } else {\n                    specificPrompt = `일상 대화: \"${userInput}\" → 공감하면서 자연스럽게 투자 이야기로 연결. 투자 관련 질문을 유도하는 친근한 제안 포함. 이모티콘 최대 2개까지 사용해서 밝고 긍정적인 분위기 연출 (2-3문장)`;\n                }\n                break;\n            default:\n                specificPrompt = `입력: \"${userInput}\" → 투자 관점에서 간결하게 응답. 이모티콘 최대 2개까지 사용해서 친근한 분위기 유지 (2-3문장)`;\n        }\n        // 맥락 정보 추가\n        if (conversationContext) {\n            specificPrompt += `\\n\\n대화 맥락: ${conversationContext}`;\n        }\n        const response = await openai.chat.completions.create({\n            model: 'gpt-4.1-nano',\n            messages: [\n                {\n                    role: 'system',\n                    content: PERSONA_SYSTEM_MESSAGE\n                },\n                {\n                    role: 'user',\n                    content: specificPrompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 120\n        });\n        const aiResponse = response.choices[0].message.content?.trim();\n        if (aiResponse) {\n            console.log(`🎭 Persona response generated for intent: ${intent}`);\n            return aiResponse;\n        }\n    } catch (error) {\n        console.error('Persona response generation failed:', error);\n    }\n    // Fallback: 기본 페르소나 응답\n    return generateFallbackPersonaResponse(userInput, intent);\n}\n// 성능 최적화된 fallback 응답 (메모리 효율적)\nconst FALLBACK_RESPONSES = {\n    greeting: [\n        '안녕하세요! 저는 금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI예요! 📈✨\\n\\n어떤 분야에 투자 관심이 있으신지 들려주세요!',\n        '안녕하세요! 저는 금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI예요! 💡🚀\\n\\n관심 있는 산업이나 기업이 있으시면 편하게 말씀해 주세요!',\n        '안녕하세요! 저는 금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI예요! ✨💎\\n\\n함께 투자 기회를 찾아보아요!'\n    ],\n    ability: [\n        '저는 금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI예요! 💪✨\\n\\nS&P 500 기업 분석과 산업 분류에 자신감 넘치게 도와드릴게요!',\n        '저는 금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI예요! 🎯🚀\\n\\nS&P 500 기업 분석과 투자 기회 발굴이 제 특기예요!'\n    ],\n    age: [\n        '저는 9주차에 태어나서 14주차에 훈련을 마쳤어요! 🎂✨\\n\\n아직 어리지만 투자 분석은 자신 있답니다!',\n        '9주차에 태어나 14주차에 훈련을 완료한 신입 투자 AI예요! 💪🚀\\n\\n나이는 어리지만 열정만큼은 누구에게도 지지 않아요!'\n    ],\n    intro: [\n        '저는 금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI예요! 🎯✨\\n\\nS&P 500 기업 분석과 산업 분류에 자신감 넘치게 도와드릴게요!',\n        '저는 금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI예요! 💡🚀\\n\\n\"반도체\", \"전기차\" 같은 키워드만 말씀해 주셔도 관련 기업들을 찾아드려요!'\n    ],\n    followUp: [\n        '네, 확실해요! 💪🔥 투자 분석은 제가 가장 자신 있는 분야거든요!\\n\\n어떤 산업이나 기업에 관심이 있으신지 말씀해 주세요!',\n        '물론이죠! 🎯💡 데이터 기반으로 정확한 분석을 해드려요!\\n\\n투자하고 싶은 분야를 알려주시면 바로 도와드릴게요!'\n    ]\n};\nfunction generateFallbackPersonaResponse(userInput, intent) {\n    const lowerInput = userInput.toLowerCase().trim();\n    switch(intent){\n        case 'greeting':\n            return FALLBACK_RESPONSES.greeting[Math.floor(Math.random() * FALLBACK_RESPONSES.greeting.length)];\n        case 'about_ai':\n            if (/^(잘.*?(할|수|있|해|될|되|가능)|할.*?(수|있|잘|될|되|가능)|가능.*?(해|할|수|있)|못.*?(해|할|수|있)|안.*?(돼|되|될|해|할))/.test(lowerInput)) {\n                return FALLBACK_RESPONSES.ability[Math.floor(Math.random() * FALLBACK_RESPONSES.ability.length)];\n            } else if (/^(몇.*?살|나이|언제.*?태어|언제.*?만들|언제.*?생|얼마나.*?됐)/.test(lowerInput) || /살.*?입니까|나이.*?입니까|몇.*?입니까/.test(lowerInput)) {\n                return FALLBACK_RESPONSES.age[Math.floor(Math.random() * FALLBACK_RESPONSES.age.length)];\n            } else {\n                return FALLBACK_RESPONSES.intro[Math.floor(Math.random() * FALLBACK_RESPONSES.intro.length)];\n            }\n        case 'casual_chat':\n            if (/^(확실|정말|진짜|맞|그래|그렇|어떻게|왜|어디서)/.test(lowerInput) && lowerInput.length <= 10) {\n                return FALLBACK_RESPONSES.followUp[Math.floor(Math.random() * FALLBACK_RESPONSES.followUp.length)];\n            }\n            const casualResponses = [\n                '그렇군요! 😄 투자 관점에서 보면 모든 일상이 기회가 될 수 있어요.\\n\\n혹시 평소 사용하는 제품이나 서비스 중에 투자하고 싶은 회사가 있나요?',\n                '흥미로운 이야기네요! 🤔 경제나 기업 뉴스도 관심 있게 보시나요?\\n\\n요즘 주목받는 산업 분야가 있으시면 함께 살펴봐요.',\n                '재미있네요! 💡 투자는 우리 일상과 밀접한 관련이 있어요.\\n\\n관심 있는 기술이나 트렌드가 있으시면 관련 투자 기회를 찾아드릴게요.',\n                '공감해요! 😊 저는 투자 분석이 전문이라서 투자 관련 질문이 있으시면 언제든 도와드릴 수 있어요.\\n\\n\"반도체\", \"전기차\", \"AI\" 같은 키워드만 말씀해 주셔도 관련 기업들을 찾아드려요!',\n                '그런 생각도 드시는군요! 🤗 저는 S&P 500 기업 분석이 특기예요.\\n\\n투자에 관심이 있으시거나 궁금한 산업 분야가 있으시면 편하게 말씀해 주세요!'\n            ];\n            return casualResponses[Math.floor(Math.random() * casualResponses.length)];\n        default:\n            const defaultResponses = [\n                '흥미로운 관점이네요! 😊 투자 측면에서 더 구체적으로 도와드릴 수 있어요.\\n\\n어떤 산업이나 기업에 관심이 있으신지 말씀해 주세요.',\n                '좋은 질문입니다! 💡 저는 투자 기회 발굴이 전문이에요.\\n\\n관심 있는 분야를 알려주시면 관련 기업들을 분석해서 추천해드리겠습니다.',\n                '도움을 드리고 싶어요! 🤝 투자 관련해서 궁금한 것이 있으시거나,\\n\\n특정 산업에 관심이 있으시면 언제든 말씀해 주세요.',\n                '그렇군요! 🌟 저는 투자 분석 전문 AI라서 투자 관련 질문에 특히 자신 있어요.\\n\\n\"바이오\", \"게임\", \"클라우드\" 같은 산업 키워드만 말씀해 주셔도 관련 기업들을 찾아드릴게요!',\n                '이해했어요! 😄 혹시 투자에 관심이 있으시다면 언제든 말씀해 주세요.\\n\\n저는 S&P 500 기업 분석과 산업 분류가 전문이거든요!'\n            ];\n            return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];\n    }\n}\n// 강화된 의도 분류 함수 (패턴 매칭 기반)\nasync function classifyUserIntent(userInput) {\n    const lowerInput = userInput.toLowerCase().trim();\n    // 1. AI 정체성 및 능력 질문 (최우선 - 확장된 패턴)\n    if (/(너|넌|당신).*?(누구|뭐|무엇|어떤|할|수|있|잘|못|가능|능력)/.test(lowerInput) || /(누구|뭐|무엇|어떤|할|수|있|잘|못|가능|능력).*?(너|넌|당신)/.test(lowerInput) || /^(누구야|누구니|뭐야|뭐니|누구세요)$/.test(lowerInput) || /^(잘.*?(할|수|있|해|될|되|가능)|할.*?(수|있|잘|될|되|가능)|가능.*?(해|할|수|있))/.test(lowerInput) || /^(못.*?(해|할|수|있)|안.*?(돼|되|될|해|할))/.test(lowerInput) || /^(몇.*?살|나이|언제.*?태어|언제.*?만들|언제.*?생|얼마나.*?됐)/.test(lowerInput) || /살.*?입니까|나이.*?입니까|몇.*?입니까/.test(lowerInput) || /자기소개|소개해|정체|신원|기능|역할|능력/.test(lowerInput)) {\n        console.log('✅ AI 능력/정체성 질문 패턴 매칭:', lowerInput);\n        return {\n            intent: 'about_ai',\n            confidence: 0.95,\n            reasoning: 'AI 정체성/능력 질문 패턴 매칭'\n        };\n    }\n    // 2. 투자 추천 요청 (일반적인 추천 요청)\n    if (/^(투자.*?추천|추천.*?투자|어떤.*?기업|어떤.*?회사|어디.*?투자|뭐.*?투자|투자.*?해줘|추천.*?해줘|좋은.*?기업|좋은.*?회사|투자.*?하면|투자.*?할까|어떤.*?좋을까|뭐.*?좋을까|아무거나.*?추천|아무.*?추천|랜덤.*?추천|무작위.*?추천|아무.*?기업|아무.*?회사)/.test(lowerInput)) {\n        return {\n            intent: 'investment_recommendation',\n            confidence: 0.95,\n            reasoning: '일반적인 투자 추천 요청 패턴 매칭'\n        };\n    }\n    // 3. 명확한 인사말\n    if (/^(안녕|하이|hi|hello|헬로|반갑|좋은|굿모닝)/.test(lowerInput)) {\n        return {\n            intent: 'greeting',\n            confidence: 0.95,\n            reasoning: '인사말 패턴 매칭'\n        };\n    }\n    // 3. 감사 표현이나 긍정적 피드백\n    if (/^(감사|고마워|고맙|잘했|좋아|훌륭|멋져|최고|완벽|잘부탁)/.test(lowerInput)) {\n        return {\n            intent: 'casual_chat',\n            confidence: 0.95,\n            reasoning: '감사/긍정 표현 패턴 매칭'\n        };\n    }\n    // 4. 기업명 직접 언급 확인 (투자 맥락이 있는 경우만)\n    for (const koreanName of Object.keys(KOREAN_COMPANY_MAPPING)){\n        if (lowerInput.includes(koreanName)) {\n            // 투자/금융 맥락이 있는지 확인\n            const hasInvestmentContext = /(투자|주식|분석|차트|매수|매도|추천|전망|수익|손실|포트폴리오)/.test(lowerInput);\n            const hasFinancialContext = /(기업|회사|산업|시장|경제|금융)/.test(lowerInput);\n            if (hasInvestmentContext || hasFinancialContext || lowerInput.length <= 10) {\n                // 명확한 투자 맥락이 있거나 단순한 기업명 언급인 경우\n                console.log(`✅ Company direct match with investment context: ${koreanName}`);\n                return {\n                    intent: 'company_direct',\n                    confidence: 0.9,\n                    reasoning: '기업명 직접 언급 (투자 맥락 포함)'\n                };\n            } else {\n                // 기업명이 포함되어 있지만 투자 맥락이 없는 경우 (예: \"치킨을 먹을지 피자를 먹을지\")\n                console.log(`⚠️ Company name mentioned but no investment context: ${koreanName} in \"${userInput}\"`);\n            }\n        }\n    }\n    // 5. 일반적인 잡담 패턴 (확장)\n    if (/^(뭐해|뭐하니|뭐하세요|뭐하고|심심|재미|날씨|오늘|어때|어떻게|괜찮|좋|나쁘|힘들)/.test(lowerInput)) {\n        return {\n            intent: 'casual_chat',\n            confidence: 0.9,\n            reasoning: '일반 잡담 패턴 매칭'\n        };\n    }\n    // 6. 명확한 투자/산업 키워드가 있는 경우만 investment_query로 분류\n    if (/(투자|주식|산업|기업|회사|종목|매수|매도|분석|추천|포트폴리오|수익|손실|시장|경제|금융|반도체|전기차|바이오|헬스케어|ai|인공지능|클라우드|에너지|은행|보험|부동산|게임|소프트웨어|항공|우주|통신|의료|제약|화학|자동차|소매|유통|식품|음료|건설|철강|섬유|미디어|엔터테인먼트)/.test(lowerInput)) {\n        return {\n            intent: 'investment_query',\n            confidence: 0.8,\n            reasoning: '투자/산업 키워드 패턴 매칭'\n        };\n    }\n    // 7. 기본값: 잡담으로 분류 (투자 관련이 아닌 경우)\n    console.log('⚠️ 명확하지 않은 입력, 잡담으로 분류:', lowerInput);\n    return {\n        intent: 'casual_chat',\n        confidence: 0.4,\n        reasoning: '명확하지 않은 입력으로 잡담 분류'\n    };\n}\n// 전체 데이터에서 기업명 검색 (START 단계용)\nfunction findCompanyInAllData(userInput) {\n    const allTickers = Object.keys(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL);\n    // 1. 티커 직접 매칭\n    const upperInput = userInput.toUpperCase().trim();\n    const directTicker = allTickers.find((ticker)=>ticker === upperInput);\n    if (directTicker) {\n        console.log(`Direct ticker match: ${userInput} -> ${directTicker}`);\n        return directTicker;\n    }\n    // 2. 한글 기업명 매핑 테이블 사용\n    const normalizedInput = userInput.trim().toLowerCase();\n    for (const [koreanName, englishNames] of Object.entries(KOREAN_COMPANY_MAPPING)){\n        if (normalizedInput.includes(koreanName)) {\n            for (const ticker of allTickers){\n                const company = _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL[ticker];\n                if (!company) continue;\n                const companyName = company.name.toLowerCase();\n                for (const englishName of englishNames){\n                    if (companyName.includes(englishName)) {\n                        console.log(`Korean company name match: \"${koreanName}\" -> ${ticker} (${company.name})`);\n                        return ticker;\n                    }\n                }\n            }\n        }\n    }\n    // 3. 영어 기업명 직접 매칭\n    for (const ticker of allTickers){\n        const company = _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL[ticker];\n        if (!company) continue;\n        const companyName = company.name.toLowerCase();\n        // 전체 이름 매칭\n        if (companyName.includes(normalizedInput) || normalizedInput.includes(companyName)) {\n            console.log(`Full company name match: \"${normalizedInput}\" -> ${ticker} (${company.name})`);\n            return ticker;\n        }\n        // 주요 단어 매칭 (3글자 이상)\n        const companyWords = companyName.split(' ').filter((word)=>word.length > 2);\n        for (const word of companyWords){\n            if (normalizedInput.includes(word) && word.length > 3) {\n                console.log(`Company word match: \"${word}\" -> ${ticker} (${company.name})`);\n                return ticker;\n            }\n        }\n    }\n    return null;\n}\n// 한글-영어 기업명 매핑\nconst KOREAN_COMPANY_MAPPING = {\n    // 주요 기술 기업\n    '인텔': [\n        'intel',\n        'intel corporation'\n    ],\n    '애플': [\n        'apple'\n    ],\n    '마이크로소프트': [\n        'microsoft'\n    ],\n    '구글': [\n        'alphabet',\n        'google'\n    ],\n    '알파벳': [\n        'alphabet'\n    ],\n    '테슬라': [\n        'tesla'\n    ],\n    '아마존': [\n        'amazon'\n    ],\n    '메타': [\n        'meta'\n    ],\n    '페이스북': [\n        'meta'\n    ],\n    '넷플릭스': [\n        'netflix'\n    ],\n    '엔비디아': [\n        'nvidia'\n    ],\n    '삼성': [\n        'samsung'\n    ],\n    '어도비': [\n        'adobe'\n    ],\n    '오라클': [\n        'oracle'\n    ],\n    '세일즈포스': [\n        'salesforce'\n    ],\n    '시스코': [\n        'cisco'\n    ],\n    // 반도체 기업\n    '퀄컴': [\n        'qualcomm'\n    ],\n    '브로드컴': [\n        'broadcom'\n    ],\n    'amd': [\n        'advanced micro devices',\n        'amd'\n    ],\n    '에이엠디': [\n        'advanced micro devices',\n        'amd'\n    ],\n    '어드밴스드': [\n        'advanced micro devices'\n    ],\n    '마이크론': [\n        'micron'\n    ],\n    '텍사스': [\n        'texas instruments'\n    ],\n    '어플라이드': [\n        'applied materials'\n    ],\n    '아날로그': [\n        'analog devices'\n    ],\n    '램리서치': [\n        'lam research'\n    ],\n    '케이엘에이': [\n        'kla'\n    ],\n    '테라다인': [\n        'teradyne'\n    ],\n    '마이크로칩': [\n        'microchip'\n    ],\n    '온세미': [\n        'on semiconductor'\n    ],\n    '스카이웍스': [\n        'skyworks'\n    ],\n    '엔엑스피': [\n        'nxp'\n    ],\n    '모놀리식': [\n        'monolithic power'\n    ],\n    // 금융 기업\n    '골드만삭스': [\n        'goldman sachs'\n    ],\n    '모건스탠리': [\n        'morgan stanley'\n    ],\n    '뱅크오브아메리카': [\n        'bank of america'\n    ],\n    '씨티그룹': [\n        'citigroup'\n    ],\n    '웰스파고': [\n        'wells fargo'\n    ],\n    '제이피모간': [\n        'jpmorgan'\n    ],\n    // 소비재 기업\n    '코카콜라': [\n        'coca-cola'\n    ],\n    '펩시': [\n        'pepsico'\n    ],\n    '맥도날드': [\n        'mcdonald'\n    ],\n    '스타벅스': [\n        'starbucks'\n    ],\n    '나이키': [\n        'nike'\n    ],\n    '디즈니': [\n        'disney'\n    ],\n    // 헬스케어\n    '존슨앤존슨': [\n        'johnson & johnson'\n    ],\n    '화이자': [\n        'pfizer'\n    ],\n    '머크': [\n        'merck'\n    ],\n    '애브비': [\n        'abbvie'\n    ],\n    // 에너지\n    '엑손모빌': [\n        'exxon mobil'\n    ],\n    '셰브론': [\n        'chevron'\n    ],\n    // 통신\n    '버라이즌': [\n        'verizon'\n    ],\n    '에이티앤티': [\n        'at&t'\n    ],\n    // 항공우주\n    '보잉': [\n        'boeing'\n    ],\n    '록히드마틴': [\n        'lockheed martin'\n    ]\n};\nfunction findTickerInText(text, availableTickers) {\n    const upperText = text.toUpperCase();\n    // 1. 티커 직접 매칭\n    const directTicker = availableTickers.find((ticker)=>upperText.includes(ticker));\n    if (directTicker) return directTicker;\n    // 2. 한글 기업명 매칭\n    const normalizedInput = text.trim().toLowerCase();\n    // 2-1. 한글-영어 매핑 테이블 사용\n    for (const [koreanName, englishNames] of Object.entries(KOREAN_COMPANY_MAPPING)){\n        if (normalizedInput.includes(koreanName)) {\n            for (const ticker of availableTickers){\n                const company = _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL[ticker];\n                if (!company) continue;\n                const companyName = company.name.toLowerCase();\n                for (const englishName of englishNames){\n                    if (companyName.includes(englishName)) {\n                        console.log(`Korean name match: \"${koreanName}\" -> ${ticker} (${company.name})`);\n                        return ticker;\n                    }\n                }\n            }\n        }\n    }\n    // 2-2. 숫자 매칭 (1, 2, 3 등)\n    const numberMatch = normalizedInput.match(/^(\\d+)$/);\n    if (numberMatch) {\n        const index = parseInt(numberMatch[1]) - 1;\n        if (index >= 0 && index < availableTickers.length) {\n            console.log(`Number match: ${numberMatch[1]} -> ${availableTickers[index]}`);\n            return availableTickers[index];\n        }\n    }\n    // 2-3. 영어 기업명 직접 매칭\n    for (const ticker of availableTickers){\n        const company = _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL[ticker];\n        if (!company) continue;\n        const companyName = company.name.toLowerCase();\n        // 전체 이름 매칭\n        if (companyName.includes(normalizedInput) || normalizedInput.includes(companyName)) {\n            console.log(`Full name match: \"${normalizedInput}\" -> ${ticker} (${company.name})`);\n            return ticker;\n        }\n        // 일반적인 부분 매칭 (영어 기업명의 주요 단어들)\n        const companyWords = companyName.split(' ').filter((word)=>word.length > 2);\n        for (const word of companyWords){\n            if (normalizedInput.includes(word) && word.length > 3) {\n                console.log(`Word match: \"${word}\" -> ${ticker} (${company.name})`);\n                return ticker;\n            }\n        }\n    }\n    return null;\n}\n//-----------------------------------------------------------\n// API 핸들러\n//-----------------------------------------------------------\n// bodyParser를 활성화하여 JSON 파싱 지원\nconst config = {\n    api: {\n        bodyParser: {\n            sizeLimit: '1mb'\n        }\n    }\n};\nasync function handler(req, res) {\n    if (req.method !== 'POST') return res.status(405).end();\n    // 요청 파싱 - JSON과 raw text 둘 다 지원\n    let userInput = '';\n    if (req.headers['content-type']?.includes('application/json')) {\n        // JSON 형식 (frontend에서 오는 경우)\n        const { message } = req.body;\n        userInput = message?.trim() || '';\n    } else {\n        // Raw text 형식\n        let body = '';\n        for await (const chunk of req)body += chunk;\n        userInput = body.trim();\n    }\n    // 세션 관리\n    let sessionId = req.cookies.sessionId;\n    if (!sessionId) {\n        sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_0__.v4)();\n        res.setHeader('Set-Cookie', `sessionId=${sessionId}; Path=/; HttpOnly; SameSite=Lax`);\n    }\n    // 세션 상태 가져오기\n    let state = SESSIONS.get(sessionId);\n    if (!state) {\n        state = {\n            stage: 'START',\n            selectedIndustry: null,\n            industryCompanies: [],\n            selectedTicker: null,\n            conversationHistory: [],\n            lastActivity: Date.now()\n        };\n    }\n    // 세션 활동 시간 업데이트\n    state.lastActivity = Date.now();\n    // 세션 리셋 요청 처리\n    if (userInput === '__RESET_SESSION__') {\n        state = {\n            stage: 'START',\n            selectedIndustry: null,\n            industryCompanies: [],\n            selectedTicker: null,\n            conversationHistory: [],\n            lastActivity: Date.now()\n        };\n        SESSIONS.set(sessionId, state);\n        return res.json({\n            reply: '새로운 검색을 시작하세요.'\n        });\n    }\n    // 새 세션이거나 빈 입력일 때만 환영 메시지 (페르소나 반영)\n    if (!userInput) {\n        const welcomeMessages = [\n            '안녕하세요! 저는 금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI예요! 📈✨\\n\\n투자하고 싶은 분야가 있으시면 편하게 말씀해 주세요!',\n            '안녕하세요! 저는 금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI예요! 💡🚀\\n\\n\"전기차\", \"AI\", \"바이오\" 같은 키워드를 자유롭게 말씀해 주세요!',\n            '안녕하세요? 저는 금융인공지능실무 과제를 위해 탄생한 맞춤 투자지원 AI예요! 🤝💎\\n\\n어떤 산업에 관심이 있으신지 말씀해 주세요!'\n        ];\n        const welcomeMessage = welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)];\n        SESSIONS.set(sessionId, state);\n        return res.json({\n            reply: welcomeMessage\n        });\n    }\n    try {\n        let reply = '';\n        // 모든 단계에서 명확한 \"아니오\"는 이전 단계로 롤백\n        if (isNegative(userInput)) {\n            if (state.stage === 'ASK_CHART') {\n                // STAGE 2 → STAGE 1 또는 START (산업 정보가 있는지 확인)\n                if (state.selectedIndustry && state.industryCompanies.length > 0) {\n                    // 산업 정보가 있으면 SHOW_INDUSTRY로 롤백\n                    state.stage = 'SHOW_INDUSTRY';\n                    state.selectedTicker = null;\n                    const companyList = state.industryCompanies.map((ticker, index)=>`${index + 1}. ${getCompanyName(ticker)} (${ticker})`).join('\\n');\n                    const totalCompaniesInIndustry = Object.entries(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL).filter(([_, company])=>company.industry === state.selectedIndustry).length;\n                    const moreText = totalCompaniesInIndustry > 5 ? `\\n\\n더 많은 기업을 보시려면 \"더보기\"라고 말씀해 주세요. (총 ${totalCompaniesInIndustry}개 기업)` : '';\n                    reply = `${state.selectedIndustry} 산업의 주요 기업들입니다:\\n\\n${companyList}${moreText}\\n\\n관심 있는 기업이 있나요?`;\n                } else {\n                    // 산업 정보가 없으면 (직접 기업명 입력 케이스) START로 롤백\n                    state.stage = 'START';\n                    state.selectedIndustry = null;\n                    state.industryCompanies = [];\n                    state.selectedTicker = null;\n                    const rollbackMessages = [\n                        '알겠습니다. 다른 관심 분야나 투자하고 싶은 산업을 말씀해 주세요.',\n                        '네, 이해했습니다. 어떤 다른 투자 아이디어가 있으신가요?',\n                        '좋습니다. 관심 있는 다른 산업이나 기업이 있으시면 말씀해 주세요.'\n                    ];\n                    reply = rollbackMessages[Math.floor(Math.random() * rollbackMessages.length)];\n                }\n            } else if (state.stage === 'SHOW_INDUSTRY') {\n                // STAGE 1 → STAGE 0 (리셋)\n                state = {\n                    stage: 'START',\n                    selectedIndustry: null,\n                    industryCompanies: [],\n                    selectedTicker: null,\n                    conversationHistory: state.conversationHistory,\n                    lastActivity: Date.now()\n                };\n                reply = '알겠습니다. 다른 관심 분야를 말씀해 주세요.';\n            }\n            SESSIONS.set(sessionId, state);\n            return res.json({\n                reply\n            });\n        }\n        // 단계별 처리\n        switch(state.stage){\n            case 'START':\n                // STAGE 0-1: OpenAI를 사용한 의도 분류\n                const intentResult = await classifyUserIntent(userInput);\n                console.log(`User intent: ${intentResult.intent} (confidence: ${intentResult.confidence})`);\n                // 의도별 처리\n                if (intentResult.intent === 'greeting' || intentResult.intent === 'about_ai' || intentResult.intent === 'casual_chat') {\n                    // 대화 맥락 생성 (최근 3개 대화만 사용)\n                    const recentHistory = state.conversationHistory.slice(-3);\n                    const conversationContext = recentHistory.length > 0 ? recentHistory.map((h)=>`사용자: ${h.user} → AI: ${h.ai}`).join('\\n') : undefined;\n                    reply = await generatePersonaResponse(userInput, intentResult.intent, conversationContext);\n                    // 대화 히스토리에 추가\n                    state.conversationHistory.push({\n                        user: userInput,\n                        ai: reply,\n                        intent: intentResult.intent,\n                        timestamp: Date.now()\n                    });\n                    // 히스토리 크기 제한 (최대 10개)\n                    if (state.conversationHistory.length > 10) {\n                        state.conversationHistory = state.conversationHistory.slice(-10);\n                    }\n                    break;\n                }\n                // 무작위 투자 추천 요청 처리\n                if (intentResult.intent === 'investment_recommendation') {\n                    const recommendation = generateRandomRecommendation();\n                    // 기업 설명들을 한글로 번역\n                    const translatedCompanies = await Promise.all(recommendation.companies.map(async (company)=>({\n                            ...company,\n                            translatedDescription: await translateDescription(company.description)\n                        })));\n                    // 산업명을 한글로 번역\n                    const industryTranslation = await translateDescription(recommendation.industry);\n                    // 응답 생성 (발랄하게 + 이모티콘 추가 + 기업명 중복 방지)\n                    const companyDescriptions = translatedCompanies.map((company)=>{\n                        // 기업명이 설명에 포함되어 있으면 콜론 형식으로, 아니면 기존 형식 유지\n                        const companyNameInDescription = company.translatedDescription.includes(company.name.split(' ')[0]);\n                        if (companyNameInDescription) {\n                            return `${company.name}(${company.ticker}) : ${company.translatedDescription}`;\n                        } else {\n                            return `${company.name}(${company.ticker})는 ${company.translatedDescription}`;\n                        }\n                    }).join('\\n\\n');\n                    const excitingIntros = [\n                        `제가 🎯 ${industryTranslation} 분야를 골라봤습니다!`,\n                        `✨ ${industryTranslation} 산업을 추천해드려요!`,\n                        `🚀 ${industryTranslation} 분야가 어떠신가요?`,\n                        `💡 ${industryTranslation} 산업은 어떠실까요?`\n                    ];\n                    const industryDescriptions = [\n                        `이 산업엔 S&P 500에 소속된 멋진 기업들이 있어요! 🏢💼`,\n                        `이 분야에는 정말 흥미로운 기업들이 많답니다! ⭐💎`,\n                        `이 산업의 대표 기업들을 소개해드릴게요! 🌟📈`,\n                        `이 분야의 주목할 만한 기업들이에요! 🎯✨`\n                    ];\n                    const randomIntro = excitingIntros[Math.floor(Math.random() * excitingIntros.length)];\n                    const randomDescription = industryDescriptions[Math.floor(Math.random() * industryDescriptions.length)];\n                    reply = `${randomIntro}\\n\\n${randomDescription}\\n\\n${companyDescriptions}\\n\\n어떤 기업이 가장 흥미로우신가요? 😊`;\n                    // 대화 히스토리에 추가\n                    state.conversationHistory.push({\n                        user: userInput,\n                        ai: reply,\n                        intent: intentResult.intent,\n                        timestamp: Date.now()\n                    });\n                    break;\n                }\n                // STAGE 0-2: 기업명 직접 입력 확인\n                if (intentResult.intent === 'company_direct') {\n                    const directCompany = findCompanyInAllData(userInput);\n                    if (directCompany) {\n                        // 기업명이 직접 입력된 경우 - 바로 차트 확인 단계로\n                        state.stage = 'ASK_CHART';\n                        state.selectedTicker = directCompany;\n                        const companyName = getCompanyName(directCompany);\n                        const directChartQuestions = [\n                            `🎯 ${companyName} (${directCompany}) 분석을 시작하시겠습니까? 📊`,\n                            `📈 ${companyName} (${directCompany}) 차트 분석을 시작해볼까요? ✨`,\n                            `🚀 ${companyName} (${directCompany})의 주가 분석을 확인해 드릴까요? 💹`\n                        ];\n                        reply = directChartQuestions[Math.floor(Math.random() * directChartQuestions.length)];\n                        break;\n                    }\n                }\n                // STAGE 0-3: 투자 관련 질문인 경우 - 산업 찾기 로직\n                if (intentResult.intent === 'investment_query' || intentResult.confidence < 0.7) {\n                    const industry = await findBestIndustry(userInput);\n                    // RAG 점수가 낮아서 잡담으로 분류된 경우\n                    if (industry === null) {\n                        console.log(`🗣️ Input classified as casual conversation due to low RAG scores: \"${userInput}\"`);\n                        console.log(`📊 RAG threshold check: score below ${RAG_THRESHOLDS.CASUAL_CONVERSATION_THRESHOLD}, treating as casual conversation`);\n                        reply = await generatePersonaResponse(userInput, 'casual_chat');\n                        // 대화 히스토리에 추가\n                        state.conversationHistory.push({\n                            user: userInput,\n                            ai: reply,\n                            intent: 'casual_chat',\n                            timestamp: Date.now()\n                        });\n                        // 히스토리 크기 제한 (최대 10개)\n                        if (state.conversationHistory.length > 10) {\n                            state.conversationHistory = state.conversationHistory.slice(-10);\n                        }\n                        break;\n                    }\n                    // 유효한 산업이 매칭된 경우\n                    const companies = getIndustryCompanies(industry);\n                    // 회사가 1개라도 있으면 진행 (5개 미만이어도 OK)\n                    if (companies.length > 0) {\n                        state.stage = 'SHOW_INDUSTRY';\n                        state.selectedIndustry = industry;\n                        state.industryCompanies = companies;\n                        const companyList = companies.map((ticker, index)=>`${index + 1}. ${getCompanyName(ticker)} (${ticker})`).join('\\n');\n                        const totalCompaniesInIndustry = Object.entries(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL).filter(([_, company])=>company.industry === industry).length;\n                        const moreText = totalCompaniesInIndustry > 5 ? `\\n\\n총 기업의 수는 ${totalCompaniesInIndustry}개입니다! 모든 기업을 보고 싶다면 '더보기' 버튼을 눌러주세요! 🔍✨` : '';\n                        const industryResponses = [\n                            `🏢 ${industry} 산업의 주요 기업들입니다!\\n\\n${companyList}${moreText}\\n\\n관심 있는 기업이 있나요? 😊`,\n                            `⭐ ${industry} 분야의 대표 기업들입니다!\\n\\n${companyList}${moreText}\\n\\n어떤 회사가 궁금하신가요? 🤔`,\n                            `💼 ${industry} 산업에는 다음과 같은 멋진 기업들이 있습니다!\\n\\n${companyList}${moreText}\\n\\n이 중에서 관심 있는 기업이 있으신가요? 💡`\n                        ];\n                        let baseReply = industryResponses[Math.floor(Math.random() * industryResponses.length)];\n                        // Enhance with LSTM data if available\n                        reply = await enhanceResponseWithLSTMData(companies, baseReply);\n                        // 기업 리스트 표시 상태 정보 추가\n                        SESSIONS.set(sessionId, state);\n                        return res.json({\n                            reply,\n                            status: 'showing_companies',\n                            hasMore: totalCompaniesInIndustry > 5 && companies.length === 5\n                        });\n                    } else {\n                        // 산업에 회사가 없는 경우 - 디버깅 정보 추가\n                        console.log(`No companies found for industry: \"${industry}\"`);\n                        console.log('Available industries in DATA:', [\n                            ...new Set(Object.values(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL).map((c)=>c.industry))\n                        ].slice(0, 10));\n                        reply = `😅 죄송합니다! \"${industry}\" 산업의 기업 정보를 찾을 수 없네요. 다른 관심 분야를 말씀해 주시면 더 좋은 추천을 드릴게요! 💡✨`;\n                    }\n                } else {\n                    // 의도가 명확하지 않은 경우\n                    reply = await generatePersonaResponse(userInput, 'casual_chat');\n                }\n                break;\n            case 'SHOW_INDUSTRY':\n                // \"더보기\" 요청 확인\n                if (/더보기|전체보기|더|모든|전체|all/i.test(userInput)) {\n                    // 해당 산업의 모든 회사 표시\n                    const allCompanies = Object.entries(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_3__.QUICK_ENRICHED_FINAL).filter(([_, company])=>company.industry === state.selectedIndustry).map(([ticker, _])=>ticker);\n                    const allCompanyList = allCompanies.map((ticker, index)=>`${index + 1}. ${getCompanyName(ticker)} (${ticker})`).join('\\n');\n                    reply = `🎉 ${state.selectedIndustry} 산업의 전체 기업 목록입니다! (총 ${allCompanies.length}개) 📊\\n\\n${allCompanyList}\\n\\n어떤 기업이 가장 흥미로우신가요? ✨`;\n                    // 전체 리스트로 업데이트\n                    state.industryCompanies = allCompanies;\n                    // 기업 리스트 표시 상태 정보 추가\n                    SESSIONS.set(sessionId, state);\n                    return res.json({\n                        reply,\n                        status: 'showing_companies',\n                        hasMore: false // 전체 리스트를 보여줬으므로 더 이상 더보기 없음\n                    });\n                }\n                // STAGE 1: 티커 선택 확인\n                const selectedTicker = findTickerInText(userInput, state.industryCompanies);\n                if (selectedTicker) {\n                    state.stage = 'ASK_CHART';\n                    state.selectedTicker = selectedTicker;\n                    const chartQuestions = [\n                        `📈 ${getCompanyName(selectedTicker)} (${selectedTicker}) 차트 분석을 시작해볼까요? (예/아니오) ✨`,\n                        `📊 ${getCompanyName(selectedTicker)} (${selectedTicker})의 차트를 확인해 드릴까요? (예/아니오) 🚀`,\n                        `💹 ${getCompanyName(selectedTicker)} (${selectedTicker}) 주가 차트를 보여드릴까요? (예/아니오) 😊`\n                    ];\n                    reply = chartQuestions[Math.floor(Math.random() * chartQuestions.length)];\n                } else {\n                    // 리스트에 없는 입력 → 다시 요청\n                    const companyList = state.industryCompanies.map((ticker, index)=>`${index + 1}. ${getCompanyName(ticker)} (${ticker})`).join('\\n');\n                    const retryMessages = [\n                        `🤗 위 목록에서 선택해 주세요!\\n\\n${companyList}\\n\\n또는 \"아니오\"라고 말씀해 주세요! 😊`,\n                        `💡 다음 기업 중에서 골라주세요!\\n\\n${companyList}\\n\\n관심 없으시면 \"아니오\"라고 해주세요! 🙂`,\n                        `✨ 이 중에서 선택해 주시거나 \"아니오\"라고 말씀해 주세요!\\n\\n${companyList} 🎯`\n                    ];\n                    reply = retryMessages[Math.floor(Math.random() * retryMessages.length)];\n                }\n                break;\n            case 'ASK_CHART':\n                // STAGE 2: 차트 요청 확인\n                if (isPositive(userInput)) {\n                    const ticker = state.selectedTicker;\n                    const chartResponses = [\n                        `🎉 ${getCompanyName(ticker)} (${ticker}) 차트입니다. SpeedTraffic도 준비하는 중! 📈`,\n                        `✨ ${getCompanyName(ticker)}는 투자해도 될까요? 같이 분석 도와드릴게요! 💹`,\n                        `🚀 ${getCompanyName(ticker)} 차트 분석을 시작할게요! 📊`\n                    ];\n                    reply = chartResponses[Math.floor(Math.random() * chartResponses.length)];\n                    // 차트 요청 후 세션 리셋 (새로운 검색을 위해)\n                    const resetState = {\n                        stage: 'START',\n                        selectedIndustry: null,\n                        industryCompanies: [],\n                        selectedTicker: null,\n                        conversationHistory: state.conversationHistory,\n                        lastActivity: Date.now()\n                    };\n                    SESSIONS.set(sessionId, resetState);\n                    // 차트 데이터와 함께 응답\n                    return res.json({\n                        reply,\n                        symbol: ticker,\n                        status: 'chart_requested'\n                    });\n                } else {\n                    // 명확하지 않은 답변 → 다시 질문\n                    const clarifyMessages = [\n                        `🤔 ${getCompanyName(state.selectedTicker)}(${state.selectedTicker}) 차트 분석을 시작해볼까요? \"예\" 또는 \"아니오\"로 답해주세요! 😊`,\n                        `💭 차트를 확인하시겠어요? \"예\" 또는 \"아니오\"로 말씀해 주세요! ✨`,\n                        `🎯 ${getCompanyName(state.selectedTicker)} 차트가 필요하신가요? \"예\"나 \"아니오\"로 답변해 주세요! 📈`\n                    ];\n                    reply = clarifyMessages[Math.floor(Math.random() * clarifyMessages.length)];\n                }\n                break;\n        }\n        SESSIONS.set(sessionId, state);\n        res.json({\n            reply\n        });\n    } catch (error) {\n        console.error('Pipeline error:', error);\n        res.status(500).json({\n            reply: '일시적인 오류가 발생했습니다. 다시 시도해 주세요.'\n        });\n    }\n}\n// 차트 요청 후 세션 리셋을 위한 별도 엔드포인트\nasync function resetSessionAfterChart(sessionId) {\n    const state = {\n        stage: 'START',\n        selectedIndustry: null,\n        industryCompanies: [],\n        selectedTicker: null,\n        conversationHistory: [],\n        lastActivity: Date.now()\n    };\n    SESSIONS.set(sessionId, state);\n}\n//-----------------------------------------------------------\n// RAG Threshold Testing Function (for debugging)\n//-----------------------------------------------------------\nasync function testRAGThresholds(userInput) {\n    console.log(`🧪 Testing RAG thresholds for input: \"${userInput}\"`);\n    const industry = await findBestIndustry(userInput);\n    const isCasualConversation = industry === null;\n    const reasoning = isCasualConversation ? `Input classified as casual conversation (RAG score below ${RAG_THRESHOLDS.CASUAL_CONVERSATION_THRESHOLD})` : `Input matched to industry: ${industry}`;\n    console.log(`🧪 Test result: ${reasoning}`);\n    return {\n        industry,\n        isCasualConversation,\n        reasoning\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/ai_chat.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fai_chat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cai_chat.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();