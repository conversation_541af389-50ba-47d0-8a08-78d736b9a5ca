#!/usr/bin/env python3
"""
Minimal test to check basic functionality
"""

print("Starting minimal test...")

try:
    import pandas as pd
    print("✓ pandas imported successfully")
except Exception as e:
    print(f"✗ pandas import failed: {e}")

try:
    import numpy as np
    print("✓ numpy imported successfully")
except Exception as e:
    print(f"✗ numpy import failed: {e}")

try:
    from pathlib import Path
    print("✓ pathlib imported successfully")
except Exception as e:
    print(f"✗ pathlib import failed: {e}")

try:
    from pandas.tseries.offsets import BDay
    print("✓ BDay imported successfully")
except Exception as e:
    print(f"✗ BDay import failed: {e}")

print("Basic imports test completed!")

# Test data file existence
try:
    ROOT_DIR = Path(__file__).resolve().parents[1]
    price_path = ROOT_DIR / 'data' / 'sp500_adj_close_3y.csv'
    volume_path = ROOT_DIR / 'data' / 'sp500_volume_3y.csv'
    
    print(f"ROOT_DIR: {ROOT_DIR}")
    print(f"Price file exists: {price_path.exists()}")
    print(f"Volume file exists: {volume_path.exists()}")
    
    if price_path.exists():
        # Try to read just the header
        df = pd.read_csv(price_path, nrows=1)
        print(f"Price file columns: {list(df.columns)}")
        
except Exception as e:
    print(f"File test failed: {e}")

print("Minimal test completed!")
