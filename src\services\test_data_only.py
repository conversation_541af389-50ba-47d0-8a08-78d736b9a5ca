#!/usr/bin/env python3
"""
Test data loading without TensorFlow imports
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import date, timedelta, datetime
from pathlib import Path
from pandas.tseries.offsets import BDay
from sklearn.preprocessing import MinMaxScaler

# Constants
WINDOW_DAYS = 75
HORIZON_DAYS = 5
RSI_PERIOD = 14
BB_PERIOD = 20
BB_STD_MULT = 2
FEATURE_COLS = [
    'Adj Close', 'Volume',
    'rsi14', 'bb_upper_pct',
    'bb_lower_pct', 'bb_width_pct'
]

def compute_indicators(df):
    """Compute technical indicators"""
    df = df.sort_index().copy()

    # Calculate RSI(14)
    delta = df['Adj Close'].diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)
    avg_gain = gain.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    avg_loss = loss.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    df['rsi14'] = rsi / 100.0

    # Calculate Bollinger Bands
    sma = df['Adj Close'].rolling(window=BB_PERIOD).mean()
    std = df['Adj Close'].rolling(window=BB_PERIOD).std()
    bb_upper = sma + (BB_STD_MULT * std)
    bb_lower = sma - (BB_STD_MULT * std)
    df['bb_upper_pct'] = ((df['Adj Close'] - bb_upper) / bb_upper * 100).clip(lower=-100, upper=100)
    df['bb_lower_pct'] = ((df['Adj Close'] - bb_lower) / bb_lower * 100).clip(lower=-100, upper=100)
    df['bb_width_pct'] = ((bb_upper - bb_lower) / sma * 100).clip(lower=0, upper=100)

    # Define target variable
    df['target'] = (df['Adj Close'].shift(-HORIZON_DAYS) > df['Adj Close']).astype(int)
    
    return df.dropna()

def adjust_to_trading_day(ts, df):
    if ts not in df.index:
        ts = ts - BDay(1)
        while ts not in df.index:
            ts -= BDay(1)
    return ts

def load_and_prepare_data(ticker, target_date_str):
    """Load and prepare data with the new logic"""
    try:
        ROOT_DIR = Path(__file__).resolve().parents[1]

        # Load price data with explicit dtype
        price_path = ROOT_DIR / 'data' / 'sp500_adj_close_3y.csv'
        print(f"Loading price data from: {price_path}")
        price_data = pd.read_csv(price_path, usecols=['Date', ticker], parse_dates=['Date'], index_col='Date', dtype={ticker: 'float64'})
        price_data.rename(columns={ticker: 'Adj Close'}, inplace=True)

        # Load volume data with explicit dtype
        volume_path = ROOT_DIR / 'data' / 'sp500_volume_3y.csv'
        print(f"Loading volume data from: {volume_path}")
        volume_data = pd.read_csv(volume_path, usecols=['Date', ticker], parse_dates=['Date'], index_col='Date', dtype={ticker: 'float64'})
        volume_data.rename(columns={ticker: 'Volume'}, inplace=True)

        # Merge and clean data
        all_data = pd.merge(price_data, volume_data, left_index=True, right_index=True, how='inner').dropna()
        all_data.sort_index(inplace=True)

        # Ensure datetime index has no time component
        all_data.index = pd.to_datetime(all_data.index.date)

        if all_data.empty:
            raise ValueError(f"No data for {ticker}.")

        # Adjust target date to trading day (Day 0)
        target_date = adjust_to_trading_day(pd.to_datetime(target_date_str), all_data)
        
        # Training data: up to and including Day -5
        train_end_date = target_date - BDay(5)  # Day -5 (inclusive)
        train_start_date = train_end_date - pd.DateOffset(years=3, months=6)  # 3.5 years
        train_data = all_data.loc[train_start_date:train_end_date]

        # Evaluation data: Days -4, -3, -2, -1, 0
        eval_start_date = target_date - BDay(4)  # Day -4
        eval_end_date = target_date  # Day 0
        eval_data = all_data.loc[eval_start_date:eval_end_date]

        if train_data.empty:
            raise ValueError(f"No training data available for {ticker} ending on {train_end_date.date()}")
        
        if eval_data.empty or len(eval_data) < 5:
            raise ValueError(f"Not enough evaluation data for {ticker} from {eval_start_date.date()} to {eval_end_date.date()}")

        print(f"Data loaded: Training from {train_data.index.min().date()} to {train_data.index.max().date()} ({len(train_data)} days)")
        print(f"Evaluation from {eval_data.index.min().date()} to {eval_data.index.max().date()} ({len(eval_data)} days)")

        return all_data, train_data, eval_data, target_date.date()

    except FileNotFoundError as e:
        raise ValueError(f"Data file not found: {e}")
    except KeyError:
        raise ValueError(f"Data for ticker '{ticker}' not found.")

def test_full_pipeline():
    """Test the full data pipeline"""
    ticker = "AAPL"
    target_date_str = "2024-10-11"
    
    print(f"Testing data pipeline for {ticker} on {target_date_str}")
    
    # Load data
    all_data, train_data, eval_data, adjusted_target_date = load_and_prepare_data(ticker, target_date_str)
    
    # Compute indicators
    print("Computing technical indicators...")
    train_indicators = compute_indicators(train_data)
    all_indicators = compute_indicators(all_data)
    
    print(f"Training indicators: {len(train_indicators)} rows")
    print(f"All indicators: {len(all_indicators)} rows")
    
    # Scale data
    print("Scaling data...")
    scaler = MinMaxScaler()
    scaler.fit(train_indicators[FEATURE_COLS])
    
    scaled_indicators = scaler.transform(all_indicators[FEATURE_COLS])
    scaled_indicators_df = pd.DataFrame(scaled_indicators, columns=FEATURE_COLS, index=all_indicators.index)
    scaled_indicators_df['target'] = all_indicators['target']
    
    print(f"Scaled indicators: {len(scaled_indicators_df)} rows")
    
    # Test index alignment
    train_indices = train_data.index.intersection(scaled_indicators_df.index)
    print(f"Training index intersection: {len(train_indices)} rows")
    
    if len(train_indices) > 0:
        print(f"Training data range: {train_indices.min()} to {train_indices.max()}")
    
    # Test evaluation data alignment
    eval_indices = eval_data.index.intersection(scaled_indicators_df.index)
    print(f"Evaluation index intersection: {len(eval_indices)} rows")
    
    if len(eval_indices) > 0:
        print(f"Evaluation data range: {eval_indices.min()} to {eval_indices.max()}")
    
    print("Data pipeline test completed successfully!")

if __name__ == "__main__":
    test_full_pipeline()
