#!/usr/bin/env python3
"""
Debug script to test LSTM fine-tuning data loading
"""

import os
import sys
import pandas as pd
from pathlib import Path
from pandas.tseries.offsets import BDay

def adjust_to_trading_day(ts, df):
    if ts not in df.index:
        ts = ts - BDay(1)
        while ts not in df.index:
            ts -= BDay(1)
    return ts

def test_data_loading():
    """Test the data loading functionality"""
    try:
        ticker = "AAPL"
        target_date_str = "2024-10-11"
        
        ROOT_DIR = Path(__file__).resolve().parents[1]
        print(f"ROOT_DIR: {ROOT_DIR}")

        # Load price data
        price_path = ROOT_DIR / 'data' / 'sp500_adj_close_3y.csv'
        print(f"Loading price data from: {price_path}")
        
        if not price_path.exists():
            print(f"ERROR: Price file does not exist: {price_path}")
            return
            
        price_data = pd.read_csv(price_path, usecols=['Date', ticker], parse_dates=['Date'], index_col='Date', dtype={ticker: 'float64'})
        price_data.rename(columns={ticker: 'Adj Close'}, inplace=True)
        print(f"Price data loaded: {len(price_data)} rows")
        print(f"Price data date range: {price_data.index.min()} to {price_data.index.max()}")

        # Load volume data
        volume_path = ROOT_DIR / 'data' / 'sp500_volume_3y.csv'
        print(f"Loading volume data from: {volume_path}")
        
        if not volume_path.exists():
            print(f"ERROR: Volume file does not exist: {volume_path}")
            return
            
        volume_data = pd.read_csv(volume_path, usecols=['Date', ticker], parse_dates=['Date'], index_col='Date', dtype={ticker: 'float64'})
        volume_data.rename(columns={ticker: 'Volume'}, inplace=True)
        print(f"Volume data loaded: {len(volume_data)} rows")
        print(f"Volume data date range: {volume_data.index.min()} to {volume_data.index.max()}")

        # Merge data
        all_data = pd.merge(price_data, volume_data, left_index=True, right_index=True, how='inner').dropna()
        all_data.sort_index(inplace=True)
        
        # Ensure datetime index has no time component
        all_data.index = pd.to_datetime(all_data.index.date)
        
        print(f"Merged data: {len(all_data)} rows")
        print(f"Merged data date range: {all_data.index.min()} to {all_data.index.max()}")
        print(f"Sample data:\n{all_data.head()}")

        # Test target date adjustment
        target_date = adjust_to_trading_day(pd.to_datetime(target_date_str), all_data)
        print(f"Target date adjusted: {target_date}")
        
        # Test training data split
        train_end_date = target_date - BDay(5)  # Day -5 (inclusive)
        train_start_date = train_end_date - pd.DateOffset(years=3, months=6)  # 3.5 years
        train_data = all_data.loc[train_start_date:train_end_date]
        
        print(f"Training data: {len(train_data)} rows from {train_data.index.min()} to {train_data.index.max()}")
        
        # Test evaluation data split
        eval_start_date = target_date - BDay(4)  # Day -4
        eval_end_date = target_date  # Day 0
        eval_data = all_data.loc[eval_start_date:eval_end_date]
        
        print(f"Evaluation data: {len(eval_data)} rows from {eval_data.index.min()} to {eval_data.index.max()}")
        
        print("Data loading test completed successfully!")
        
    except Exception as e:
        print(f"ERROR in data loading test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_loading()
