#!/usr/bin/env python3
"""
Quick test script to verify the refactored LSTM fine-tuning service
"""

import sys
import json
from datetime import date
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """Test if all imports work correctly"""
    try:
        print("Testing imports...", file=sys.stderr)
        
        # Test basic imports
        import numpy as np
        import pandas as pd
        from pandas.tseries.offsets import BDay
        from sklearn.preprocessing import MinMaxScaler
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        import joblib
        
        print("✓ Basic imports successful", file=sys.stderr)
        
        # Test TensorFlow imports
        import tensorflow as tf
        from tensorflow.keras.models import Sequential
        from tensorflow.keras.layers import LSTM, Dense, Dropout
        from tensorflow.keras.callbacks import EarlyStopping
        
        print("✓ TensorFlow imports successful", file=sys.stderr)
        
        return True
        
    except Exception as e:
        print(f"✗ Import error: {e}", file=sys.stderr)
        return False

def test_basic_functions():
    """Test basic functions from the refactored code"""
    try:
        print("Testing basic functions...", file=sys.stderr)
        
        # Import the refactored module
        import lstm_finetuning as lstm
        
        # Test constants
        assert lstm.WINDOW_DAYS == 75
        assert lstm.HORIZON_DAYS == 5
        print("✓ Constants loaded correctly", file=sys.stderr)
        
        # Test date generation
        random_date = lstm.get_random_target_date()
        assert isinstance(random_date, date)
        print(f"✓ Random date generation works: {random_date}", file=sys.stderr)
        
        # Test focal loss function
        focal_loss_fn = lstm.focal_loss(gamma=2.0)
        print("✓ Focal loss function created", file=sys.stderr)
        
        # Test model building
        model = lstm.build_lstm_model((75, 6))
        print("✓ LSTM model built successfully", file=sys.stderr)
        
        return True
        
    except Exception as e:
        print(f"✗ Function test error: {e}", file=sys.stderr)
        return False

def test_data_loading():
    """Test data loading functions"""
    try:
        print("Testing data loading...", file=sys.stderr)
        
        import lstm_finetuning as lstm
        
        # Test ticker loading
        tickers, company_names = lstm.load_sp500_tickers()
        assert len(tickers) > 0
        assert len(company_names) > 0
        print(f"✓ Loaded {len(tickers)} tickers", file=sys.stderr)
        
        # Test available dates
        available_dates = lstm.get_available_dates()
        assert len(available_dates) > 0
        print(f"✓ Loaded {len(available_dates)} available dates", file=sys.stderr)
        
        return True
        
    except Exception as e:
        print(f"✗ Data loading test error: {e}", file=sys.stderr)
        return False

def main():
    """Run all tests"""
    print("=== LSTM Refactor Test Suite ===", file=sys.stderr)
    
    tests = [
        ("Imports", test_imports),
        ("Basic Functions", test_basic_functions),
        ("Data Loading", test_data_loading)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---", file=sys.stderr)
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED", file=sys.stderr)
        else:
            print(f"✗ {test_name} FAILED", file=sys.stderr)
    
    print(f"\n=== Results: {passed}/{total} tests passed ===", file=sys.stderr)
    
    if passed == total:
        print("🎉 All tests passed! Refactor looks good.", file=sys.stderr)
        return 0
    else:
        print("❌ Some tests failed. Check the errors above.", file=sys.stderr)
        return 1

if __name__ == "__main__":
    sys.exit(main())
