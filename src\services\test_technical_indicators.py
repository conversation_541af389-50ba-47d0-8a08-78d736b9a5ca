#!/usr/bin/env python3
"""
Simple test script for technical indicators implementation
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).resolve().parents[1]))

# Import our functions
from lstm_finetuning import compute_indicators, create_sequences, WINDOW_DAYS, HORIZON_DAYS

def test_technical_indicators():
    """Test the technical indicators implementation"""
    print("Testing technical indicators implementation...")
    
    # Create sample data
    dates = pd.date_range('2023-01-01', periods=200, freq='D')
    np.random.seed(42)
    
    # Generate realistic price data
    price_base = 100
    price_changes = np.random.normal(0, 0.02, 200)
    prices = [price_base]
    for change in price_changes[1:]:
        prices.append(prices[-1] * (1 + change))
    
    # Generate volume data
    volumes = np.random.randint(1000000, 10000000, 200)
    
    # Create DataFrame
    df = pd.DataFrame({
        'Date': dates,
        'Adj Close': prices,
        'Volume': volumes
    })
    
    print(f"Sample data shape: {df.shape}")
    print(f"Date range: {df['Date'].min()} to {df['Date'].max()}")
    
    try:
        # Test compute_indicators
        indicators = compute_indicators(df)
        print(f"Indicators shape: {indicators.shape}")
        print(f"Indicators columns: {list(indicators.columns)}")
        print(f"Sample indicators:")
        print(indicators.head())
        
        # Test create_sequences
        X, y = create_sequences(indicators)
        print(f"Sequences shape: X={X.shape}, y={y.shape}")
        print(f"Expected X shape: (samples, {WINDOW_DAYS}, 6)")
        print(f"Label balance: {y.mean():.3f}")
        
        # Verify shapes
        expected_samples = len(indicators) - WINDOW_DAYS - HORIZON_DAYS
        if X.shape == (expected_samples, WINDOW_DAYS, 6):
            print("✅ Shape verification passed!")
        else:
            print(f"❌ Shape mismatch: expected ({expected_samples}, {WINDOW_DAYS}, 6), got {X.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_technical_indicators()
    sys.exit(0 if success else 1)
